apiVersion: v1
kind: Service
metadata:
  name: {{ include "sd-score-rule.fullname" . }}
  labels:
{{ include "sd-score-rule.labels" . | indent 4 }}
    service-discovery: gateway
  namespace: {{ .Values.namespace }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.appConfig.server.port }}
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: {{ include "sd-score-rule.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
