apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Chart.Name }}-config
  namespace: {{ .Values.namespace }}
data:
  application.yml: |-
    spring:
      profiles:
        active : {{ .Values.appConfig.activeProfile }}
      datasource:
        url: jdbc:postgresql://{{ .Values.appConfig.postgres.hostname }}:{{ .Values.appConfig.postgres.port }}/score_rule{{ .Values.appConfig.postgres.flags }}
        username: {{ .Values.appConfig.postgres.username }}
        password: {{ .Values.appConfig.postgres.password }}
      rabbitmq:
        host: {{ .Values.appConfig.rabbitmq.host }}
        username: {{ .Values.appConfig.rabbitmq.username }}
        password: {{ .Values.appConfig.rabbitmq.password }}
        virtual-host: {{ .Values.appConfig.rabbitmq.virtualHost }}
    server:
      port: {{ .Values.appConfig.server.port }}
    client:
      iam:
        basePath: {{ .Values.appConfig.client.iam.basePath }}
      sales:
        basePath: {{ .Values.appConfig.client.sales.basePath }}
      config:
        basePath: {{ .Values.appConfig.client.config.basePath }}
      search:
        basePath: {{ .Values.appConfig.client.search.basePath }}
      product:
        basePath: {{ .Values.appConfig.client.product.basePath }}
      call:
        basePath: {{ .Values.appConfig.client.call.basePath }}
      email:
        basePath: {{ .Values.appConfig.client.email.basePath }}
      message:
        basePath: {{ .Values.appConfig.client.message.basePath }}
      company:
        basePath: {{ .Values.appConfig.client.company.basePath }}