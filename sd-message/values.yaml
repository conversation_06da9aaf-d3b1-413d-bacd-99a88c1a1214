# Default values for sd-message.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 2
namespace: sling-sales

image:
  repository: nexus.sling-dev.com:8023/sling/sd-message
  migrationRepository: nexus.sling-dev.com:8023/sling/sd-message/db-migration
  sidekiqRepository: nexus.sling-dev.com:8023/sling/sd-message/sidekiq
  tag: latest
  pullPolicy: Always

imagePullSecrets:
  - name: registry-credentials
nameOverride: ""
fullnameOverride: ""

service:
  type: ClusterIP
  port: 80

deployment:
  annotations:
    buildNumber: "replace-this"

ingress:
  enabled: false
  annotations: {}
    # kubernetes.io/ingress.class: nginx
  # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths: []

  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
#   memory: 128Mi

nodeSelector: {}

tolerations: []

affinity:
  nodeAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 75
        preference:
          matchExpressions:
            - key: highMemory
              operator: In
              values:
                - sd-high-memory-service-pods
      - weight: 50
        preference:
          matchExpressions:
            - key: service
              operator: In
              values:
                - sd-microservices-label
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchExpressions:
            - key: app.kubernetes.io/instance
              operator: In
              values:
                - sd-message
        topologyKey: "kubernetes.io/hostname"

rollingUpdate:
  maxSurge: 1
  maxUnavailable: 0

appConfig:
  activeProfile: qa
  server:
    port: 8080
  logoutput: true
  postgres:
    hostname: postgresql-postgresql
    username: sling_sales
    password: test
    port: 5432
    flags: ""
    dbName: message
    sidekiqPoolSize: 5
  credentials:
    masterKey: ec0c87f364c930701485db442659e182
  rabbitmq:
    host: rabbitmq
    username: sling_sales
    password: test
    virtualHost: /
  redis:
    host: "redis://redis-master:6379/2"
  s3:
    endpoint: https://nyc3.digitaloceanspaces.com
    region: nyc3
    bucketName: ''
    key: Q3HPYXCYNPT55Y6NZF2M
    secret: igJTnB2hCdo1ulSkvUAgsT0QpiGUkJGL4m9Zfr5GdVU
  facebook:
    clientId: '929969855373036'
    clientSecret: 6232aaea2d9d9ad9e378c6c56f3df460
  credential:
      encryption:
        secret: 'some-secret'
        iv: 'some-iv'
  interakt:
    solutionId: '995561112070971'
    partnerToken: ''
