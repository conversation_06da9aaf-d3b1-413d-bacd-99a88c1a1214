apiVersion: apps/v1
kind: Deployment
metadata:
  name: sd-message-sidekiq
  labels:
{{ include "sd-message-sidekiq.labels" . | indent 4 }}
  namespace: {{ .Values.namespace }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: sd-message-sidekiq
      app.kubernetes.io/instance: sd-message-sidekiq
  template:
    metadata:
      labels:
        app.kubernetes.io/name: sd-message-sidekiq
        app.kubernetes.io/instance: sd-message-sidekiq
        buildNumber: {{ .Values.deployment.annotations.buildNumber | quote }}
    spec:
    {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
    {{- end }}
      containers:
        - name: "{{ .Chart.Name }}-sidekiq"
          image: "{{ .Values.image.sidekiqRepository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
          - name: DATABASE_HOST
            value: {{ .Values.appConfig.postgres.hostname }}
          - name: DATABASE_PORT
            value: {{ .Values.appConfig.postgres.port  | quote }}
          - name: DATABASE_USER
            value: {{ .Values.appConfig.postgres.username }}
          - name: DATABASE_PASSWORD
            value: {{ .Values.appConfig.postgres.password }}
          - name: DATABASE_NAME
            value: {{ .Values.appConfig.postgres.dbName }}
          - name: DATABASE_POOL
            value: {{ .Values.appConfig.postgres.sidekiqPoolSize | quote }}
          - name: RAILS_MASTER_KEY
            value: {{ .Values.appConfig.credentials.masterKey }}
          - name: RABBITMQ_USER
            value: {{ .Values.appConfig.rabbitmq.username }}
          - name: RABBITMQ_PASSWORD
            value: {{ .Values.appConfig.rabbitmq.password }}
          - name: RABBITMQ_VIRTUAL_HOST
            value: {{ .Values.appConfig.rabbitmq.virtualHost }}
          - name: RABBITMQ_HOST
            value: {{ .Values.appConfig.rabbitmq.host }}
          - name: RAILS_LOG_TO_STDOUT
            value: {{ .Values.appConfig.logoutput | quote }}
          - name: REDIS_HOST
            value: {{ .Values.appConfig.redis.host }}
          - name: AWS_REGION
            value: {{ .Values.appConfig.s3.region | quote }}
          - name: AWS_ENDPOINT
            value: {{ .Values.appConfig.s3.endpoint | quote }}
          - name: AWS_ACCESS_KEY_ID
            value: {{ .Values.appConfig.s3.key | quote }}
          - name: AWS_SECRET_ACCESS_KEY
            value: {{ .Values.appConfig.s3.secret | quote }}
          - name: S3_ATTACHMENT_BUCKET
            value: {{ .Values.appConfig.s3.bucketName | quote }}
          - name: FACEBOOK_CLIENT_ID
            value: {{ .Values.appConfig.facebook.clientId | quote }}
          - name: FACEBOOK_CLIENT_SECRET
            value: {{ .Values.appConfig.facebook.clientSecret | quote }}
          - name: MESSAGE_CREDENTIAL_ENCRYPTION_SECRET
            value: {{ .Values.appConfig.credential.encryption.secret | quote }}
          - name: MESSAGE_CREDENTIAL_ENCRYPTION_IV
            value: {{ .Values.appConfig.credential.encryption.iv | quote }}
          - name: INTERAKT_SOLUTION_ID
            value: {{ .Values.appConfig.interakt.solutionId | quote }}
          - name: INTERAKT_PARTNER_TOKEN
            value: {{ .Values.appConfig.interakt.partnerToken | quote }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
