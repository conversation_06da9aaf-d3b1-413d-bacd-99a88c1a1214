apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "sd-communication.fullname" . }}-test-connection"
  labels:
{{ include "sd-communication.labels" . | indent 4 }}
  annotations:
    "helm.sh/hook": test-success
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args:  ['{{ include "sd-communication.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
