apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Chart.Name }}-config
  namespace: {{ .Values.namespace }}
data:
  application.properties: |
    server.port: {{ .Values.appConfig.server.port }}
    core.rabbitmq.host: {{ .Values.appConfig.rabbitmq.host }}
    core.rabbitmq.username: {{ .Values.appConfig.rabbitmq.username }}
    core.rabbitmq.password: {{ .Values.appConfig.rabbitmq.password }}
    core.rabbitmq.virtualHost: {{ .Values.appConfig.rabbitmq.virtualHost }}
    spring.datasource.url: jdbc:postgresql://{{ .Values.appConfig.postgres.hostname }}:{{ .Values.appConfig.postgres.port }}/communication{{ .Values.appConfig.postgres.flags }}
    spring.datasource.username: {{ .Values.appConfig.postgres.username }}
    spring.datasource.password: {{ .Values.appConfig.postgres.password }}
    mailgun.smtp.host: {{ .Values.appConfig.mailgun.smtp.host }}
    mailgun.smtp.username: {{ .Values.appConfig.mailgun.smtp.username }}
    mailgun.smtp.password: {{ .Values.appConfig.mailgun.smtp.password }}
    mailgun.api.base.url: {{ .Values.appConfig.mailgun.api.base.url }}
    mailgun.api.messages.url: {{ .Values.appConfig.mailgun.api.messages.url }}
    mailgun.api.username:  {{ .Values.appConfig.mailgun.api.username }}
    mailgun.mail.from:  {{ .Values.appConfig.mailgun.mail.from }}
    mailgun.api.password: {{ .Values.appConfig.mailgun.api.password }}
    ui.host.url: {{ .Values.appConfig.ui.host.url }}
    client.entity.basePath: {{ .Values.appConfig.client.entity.basePath}}
    client.iam.basePath: {{ .Values.appConfig.client.iam.basePath}}
    app.name: {{ .Values.appConfig.app.name }}
    zepto.mail.from: {{ .Values.appConfig.zepto.mail.from }}
    zepto.mail.apiUrl: {{ .Values.appConfig.zepto.mail.apiUrl }}
    zepto.mail.token: {{ .Values.appConfig.zepto.mail.token }}
    zepto.smtp.host: {{ .Values.appConfig.zepto.smtp.host }}
    zepto.smtp.username: {{ .Values.appConfig.zepto.smtp.username }}
    zepto.smtp.password: {{ .Values.appConfig.zepto.smtp.password }}

