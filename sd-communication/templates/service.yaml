apiVersion: v1
kind: Service
metadata:
  name: {{ include "sd-communication.fullname" . }}
  labels:
{{ include "sd-communication.labels" . | indent 4 }}
  namespace: {{ .Values.namespace }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.appConfig.server.port }}
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: {{ include "sd-communication.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
