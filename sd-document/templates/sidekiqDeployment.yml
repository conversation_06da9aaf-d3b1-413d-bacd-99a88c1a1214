apiVersion: apps/v1
kind: Deployment
metadata:
  name: sd-document-sidekiq
  labels:
{{ include "sd-document-sidekiq.labels" . | indent 4 }}
  namespace: {{ .Values.namespace }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: sd-document-sidekiq
      app.kubernetes.io/instance: sd-document-sidekiq
  template:
    metadata:
      labels:
        app.kubernetes.io/name: sd-document-sidekiq
        app.kubernetes.io/instance: sd-document-sidekiq
        buildNumber: {{ .Values.deployment.annotations.buildNumber | quote }}
    spec:
    {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
    {{- end }}
      containers:
        - name: "{{ .Chart.Name }}-sidekiq"
          image: "{{ .Values.image.sidekiqRepository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
          - name: DATABASE_HOST
            value: {{ .Values.appConfig.postgres.hostname }}
          - name: DATABASE_PORT
            value: {{ .Values.appConfig.postgres.port  | quote }}
          - name: DATABASE_USER
            value: {{ .Values.appConfig.postgres.username }}
          - name: DATABASE_PASSWORD
            value: {{ .Values.appConfig.postgres.password }}
          - name: DATABASE_NAME
            value: {{ .Values.appConfig.postgres.dbName }}
          - name: DATABASE_POOL
            value: {{ .Values.appConfig.postgres.sidekiqPoolSize | quote }}
          - name: RAILS_MASTER_KEY
            value: {{ .Values.appConfig.credentials.masterKey }}
          - name: RABBITMQ_USER
            value: {{ .Values.appConfig.rabbitmq.username }}
          - name: RABBITMQ_PASSWORD
            value: {{ .Values.appConfig.rabbitmq.password }}
          - name: RABBITMQ_VIRTUAL_HOST
            value: {{ .Values.appConfig.rabbitmq.virtualHost }}
          - name: RABBITMQ_HOST
            value: {{ .Values.appConfig.rabbitmq.host }}
          - name: RAILS_LOG_TO_STDOUT
            value: {{ .Values.appConfig.logoutput | quote }}
          - name: REDIS_HOST
            value: {{ .Values.appConfig.redis.host }}
          - name: AWS_REGION
            value: {{ .Values.appConfig.s3.region | quote }}
          - name: AWS_ENDPOINT
            value: {{ .Values.appConfig.s3.endpoint | quote }}
          - name: AWS_ACCESS_KEY_ID
            value: {{ .Values.appConfig.s3.key | quote }}
          - name: AWS_SECRET_ACCESS_KEY
            value: {{ .Values.appConfig.s3.secret | quote }}
          - name: S3_DOCUMENT_BUCKET
            value: {{ .Values.appConfig.s3.documentBucket | quote }}
          - name: S3_FOLDER_NAME
            value: {{ .Values.appConfig.s3.folderName | quote }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}