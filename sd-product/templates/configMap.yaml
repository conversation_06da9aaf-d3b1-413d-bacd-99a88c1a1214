apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Chart.Name }}-config
  namespace: {{ .Values.namespace }}
data:
  application.yml: |-
    spring:
      profiles:
        active : {{ .Values.appConfig.activeProfile }}
      datasource:
        url: jdbc:postgresql://{{ .Values.appConfig.postgres.hostname }}:{{ .Values.appConfig.postgres.port }}/product{{ .Values.appConfig.postgres.flags }}
        username: {{ .Values.appConfig.postgres.username }}
        password: {{ .Values.appConfig.postgres.password }}
        hikari:
          maximum-pool-size: {{ .Values.appConfig.postgres.hikari.maximumPoolSize }}
      rabbitmq:
        host: {{ .Values.appConfig.rabbitmq.host }}
        port: 5672
        username: {{ .Values.appConfig.rabbitmq.username }}
        password: {{ .Values.appConfig.rabbitmq.password }}
        virtual-host: {{ .Values.appConfig.rabbitmq.virtualHost }}
    server:
      port: {{ .Values.appConfig.server.port }}
    client:
      config:
        basePath: {{ .Values.appConfig.client.config.basePath }}
      sales:
        basePath: {{ .Values.appConfig.client.sales.basePath }}
      deal:
        basePath: {{ .Values.appConfig.client.deal.basePath }}
      iam:
        basePath: {{ .Values.appConfig.client.iam.basePath }}
    s3:
      product:
        image:
          endpoint: {{ .Values.appConfig.s3.product.image.endpoint }}
          region: {{ .Values.appConfig.s3.product.image.region }}
          key: {{ .Values.appConfig.s3.product.image.key }}
          secret: {{ .Values.appConfig.s3.product.image.secret }}
          bucket-name: {{ .Values.appConfig.s3.product.image.bucketName }}
    memcached:
      host: {{ .Values.appConfig.memcached.host }}
