apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "sd-productivity.fullname" . }}-test-connection"
  labels:
{{ include "sd-productivity.labels" . | indent 4 }}
  annotations:
    "helm.sh/hook": test-success
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args:  ['{{ include "sd-productivity.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
