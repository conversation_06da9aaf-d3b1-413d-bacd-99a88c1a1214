apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Chart.Name }}-config
  namespace: {{ .Values.namespace }}
data:
  application.properties: |
    server.port: {{ .Values.appConfig.server.port }}
    spring.profiles.active: {{ .Values.appConfig.activeProfile }}
    core.rabbitmq.host: {{ .Values.appConfig.rabbitmq.host }}
    core.rabbitmq.username: {{ .Values.appConfig.rabbitmq.username }}
    core.rabbitmq.password: {{ .Values.appConfig.rabbitmq.password }}
    core.rabbitmq.virtualHost: {{ .Values.appConfig.rabbitmq.virtualHost }}
    spring.datasource.url: jdbc:postgresql://{{ .Values.appConfig.postgres.hostname }}:{{ .Values.appConfig.postgres.port }}/productivity{{ .Values.appConfig.postgres.flags }}
    spring.datasource.username: {{ .Values.appConfig.postgres.username }}
    spring.datasource.password: {{ .Values.appConfig.postgres.password }}
    client.iam.basePath: {{ .Values.appConfig.client.iam.basePath }}
    client.entity.basePath: {{ .Values.appConfig.client.entity.basePath }}
    client.sales.basePath: {{ .Values.appConfig.client.sales.basePath }}
    client.deal.basePath: {{ .Values.appConfig.client.deal.basePath }}
    client.meetings.basePath: {{ .Values.appConfig.client.meetings.basePath }}
    client.search.basePath: {{ .Values.appConfig.client.search.basePath }}
    client.company.basePath: {{ .Values.appConfig.client.company.basePath }}
    elasticsearch.host: {{ .Values.appConfig.elasticsearch.host }}
    elasticsearch.port: {{ .Values.appConfig.elasticsearch.port }}
    newElasticsearch.host: {{ .Values.appConfig.newElasticsearch.host }}
    newElasticsearch.port: {{ .Values.appConfig.newElasticsearch.port }}
    logging.level.root: WARN
    logging.level.org.springframework.web: WARN
    logging.level.org.hibernate: ERROR
    spring.datasource.hikari.maximum-pool-size: {{ .Values.appConfig.datasource.hikari.maximumPoolSize }}
    flyway.enabled: false
