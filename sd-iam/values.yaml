# Default values for sd-iam.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 2
namespace: sling-sales

image:
  repository: nexus.sling-dev.com:8023/sling/sd-iam
  migrationRepository: nexus.sling-dev.com:8023/sling/sd-iam/db-migration
  tag: latest
  pullPolicy: Always

imagePullSecrets:
  - name: registry-credentials
nameOverride: ""
fullnameOverride: ""

deployment:
  annotations:
    buildNumber: "replace-this"

service:
  type: ClusterIP
  port: 80

ingress:
  enabled: false
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths: []

  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
   requests:
  #   cpu: 100m
     memory: 1800Mi

autoscaling:
  enabled: false
  minReplicas: 2
  maxReplicas: 7
  #    targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 90

nodeSelector: {}

tolerations: []

affinity:
  nodeAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
    - weight: 75
      preference:
        matchExpressions:
        - key: highMemory
          operator: In
          values:
          - sd-high-memory-service-pods
    - weight: 50
      preference:
        matchExpressions:
          - key: service
            operator: In
            values:
              - sd-microservices-label
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchExpressions:
            - key: app.kubernetes.io/instance
              operator: In
              values:
                - sd-iam
        topologyKey: "kubernetes.io/hostname"

rollingUpdate:
  maxSurge: 1
  maxUnavailable: 0

appConfig:
  activeProfile: QA
  server:
    port: 8080
  rabbitmq:
    host: rabbitmq
    virtualHost: /
    username: sling_sales
    password: test
  redis:
    host: redis-master
    port: 6379
    password: ""
    namespace: qa
    useSsl: false
  postgres:
    hostname: postgresql-postgresql
    username: sling_sales
    password: test
    port: 5432
    flags: ""
  elasticsearch:
    host: elasticsearch-master
    port: 9200
  newElasticsearch:
    host: new-elasticsearch-master
    port: 9201
  client:
    entity:
      basePath: http://sd-config
    sales:
      basePath: http://sd-sales
    emailEngine:
      basePath: http://sd-email-engine
  google:
    recaptcha:
      secretKey: change-this
      projectId: kylas-recaptcha-1692345199527
      siteKey:
        web: 6LdC2M4nAAAAAI8ixcCVYXHFa61WrWGwAAV-_U5J
        android: 6LcHG9MnAAAAAOlY9wSdBPoRd7bgN89Eb6naUMlJ
        ios: 6LftSdMnAAAAALI-iCszhki2PpAN3v0Bj0T6G1JZ
      apiKey: some-api-key
    calendar:
      oauth:
        client:
          id: 'some-id'
          secret: 'some-secret'
  microsoft:
    calendar:
      oauth:
        client:
          id: 'some-id'
          secret: 'some-secret'
        new:
          client:
            id: 'some-id'
            secret: 'some-secret'
  billing:
    chargebee:
      siteName: kylas-sandbox-test
      apiKey: test_mWJz7H1EpFqhEadwHRPJU5O3E1ZQddMe
  ui:
    service:
      hostName: ''

  api:
    domain:
      url: ''
  spring:
    datasource:
      hikari:
        maximumPoolSize: 15
        minimumIdle: 5
        idleTimeout: 30000
        poolName: SpringBootJPAHikariCP
        connectionTimeout: 30000
  gmail:
    oauth:
      client:
        id: 'some-id'
        secret:  'some-secret'
  outlook:
    oauth:
      client:
        id: 'some-id'
        secret:  'some-secret'
      new:
        client:
          id: 'some-id'
          secret: 'some-secret'
  memcached:
    host: memcached
  s3:
    endpoint: https://sgp1.digitaloceanspaces.com
    region: sgp1
    bucketName: tenant-logo
    key: updated-by-jenkins
    secret: updated-by-jenkins
    logoFolder: qa
  app:
    name: kylas
    default:
      plan:
        name: ELEVATE_MONTHLY
  cdn:
    host:
      name: https://assets.kylas.io
  email:
    credential:
      encryption:
        secret: some-secret
        iv: some-iv
  emailEngine:
    proxy:
      address: http://localhost
      tenantIds: 0