apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Chart.Name }}-config
  namespace: {{ .Values.namespace }}
data:
  application.properties: |
    spring.profiles.active={{ .Values.appConfig.activeProfile }}
    core.rabbitmq.host: {{ .Values.appConfig.rabbitmq.host }}
    core.rabbitmq.username: {{ .Values.appConfig.rabbitmq.username }}
    core.rabbitmq.password: {{ .Values.appConfig.rabbitmq.password }}
    core.rabbitmq.virtualHost: {{ .Values.appConfig.rabbitmq.virtualHost }}
    spring.datasource.url: jdbc:postgresql://{{ .Values.appConfig.postgres.hostname }}:{{ .Values.appConfig.postgres.port }}/iam{{ .Values.appConfig.postgres.flags }}
    spring.datasource.username: {{ .Values.appConfig.postgres.username }}
    spring.datasource.password: {{ .Values.appConfig.postgres.password }}
    server.port: {{ .Values.appConfig.server.port }}
    client.sales.basePath: {{ .Values.appConfig.client.sales.basePath }}
    client.entity.basePath: {{ .Values.appConfig.client.entity.basePath }}
    client.emailEngine.basePath: {{ .Values.appConfig.client.emailEngine.basePath }}
    google.recaptcha.secretKey: {{ .Values.appConfig.google.recaptcha.secretKey }}
    elasticsearch.host: {{ .Values.appConfig.elasticsearch.host }}
    elasticsearch.port: {{ .Values.appConfig.elasticsearch.port }}
    newElasticsearch.host: {{ .Values.appConfig.newElasticsearch.host }}
    newElasticsearch.port: {{ .Values.appConfig.newElasticsearch.port }}
    logging.level.root: WARN
    logging.level.org.springframework.web: WARN
    logging.level.org.hibernate: ERROR
    ui.service.hostName: {{ .Values.appConfig.ui.service.hostName }}
    api.domain.url: {{ .Values.appConfig.api.domain.url }}
    gmail.oauth.client.id: {{ .Values.appConfig.gmail.oauth.client.id }}
    gmail.oauth.client.secret: {{ .Values.appConfig.gmail.oauth.client.secret }}
    google.calendar.oauth.client.id: {{ .Values.appConfig.google.calendar.oauth.client.id }}
    google.calendar.oauth.client.secret: {{ .Values.appConfig.google.calendar.oauth.client.secret }}
    microsoft.calendar.oauth.client.id: {{ .Values.appConfig.microsoft.calendar.oauth.client.id }}
    microsoft.calendar.oauth.client.secret: {{ .Values.appConfig.microsoft.calendar.oauth.client.secret }}
    microsoft.calendar.oauth.new.client.id: {{ .Values.appConfig.microsoft.calendar.oauth.new.client.id }}
    microsoft.calendar.oauth.new.client.secret: {{ .Values.appConfig.microsoft.calendar.oauth.new.client.secret }}
    outlook.oauth.client.id: {{ .Values.appConfig.outlook.oauth.client.id }}
    outlook.oauth.client.secret: {{ .Values.appConfig.outlook.oauth.client.secret }}
    outlook.oauth.new.client.id: {{ .Values.appConfig.outlook.oauth.new.client.id }}
    outlook.oauth.new.client.secret: {{ .Values.appConfig.outlook.oauth.new.client.secret }}
    billing.chargebee.site-name: {{ .Values.appConfig.billing.chargebee.siteName }}
    billing.chargebee.api-key: {{ .Values.appConfig.billing.chargebee.apiKey }}
    memcached.host: {{ .Values.appConfig.memcached.host }}
    s3.endpoint: {{ .Values.appConfig.s3.endpoint }}
    s3.region: {{ .Values.appConfig.s3.region }}
    s3.key: {{ .Values.appConfig.s3.key }}
    s3.secret: {{ .Values.appConfig.s3.secret }}
    s3.bucket-name: {{ .Values.appConfig.s3.bucketName }}
    s3.logo-folder: {{ .Values.appConfig.s3.logoFolder }}
    spring.datasource.hikari.maximum-pool-size: {{ .Values.appConfig.spring.datasource.hikari.maximumPoolSize }}
    spring.datasource.hikari.minimum-idle: {{ .Values.appConfig.spring.datasource.hikari.minimumIdle }}
    spring.datasource.hikari.idle-timeout: {{ .Values.appConfig.spring.datasource.hikari.idleTimeout }}
    spring.datasource.hikari.pool-name: {{ .Values.appConfig.spring.datasource.hikari.poolName }}
    spring.datasource.hikari.connection-timeout: {{ .Values.appConfig.spring.datasource.hikari.connectionTimeout }}
    app.name: {{ .Values.appConfig.app.name }}
    cdn.host.name: {{ .Values.appConfig.cdn.host.name }}
    app.default.plan.name: {{ .Values.appConfig.app.default.plan.name }}
    email.credential.encryption.secret: {{ .Values.appConfig.email.credential.encryption.secret }}
    email.credential.encryption.iv: {{ .Values.appConfig.email.credential.encryption.iv }}
    google.recaptcha.siteKey.web: {{ .Values.appConfig.google.recaptcha.siteKey.web }}
    google.recaptcha.siteKey.android: {{ .Values.appConfig.google.recaptcha.siteKey.android }}
    google.recaptcha.siteKey.ios: {{ .Values.appConfig.google.recaptcha.siteKey.ios }}
    google.recaptcha.apiKey: {{ .Values.appConfig.google.recaptcha.apiKey }}
    google.recaptcha.projectId: {{ .Values.appConfig.google.recaptcha.projectId }}
    flyway.enabled: false
    redis.host: {{ .Values.appConfig.redis.host }}
    redis.port: {{ .Values.appConfig.redis.port }}
    redis.password: {{ .Values.appConfig.redis.password }}
    redis.useSsl: {{ .Values.appConfig.redis.useSsl }}
    redis.namespace: {{ .Values.appConfig.redis.namespace }}
    email-engine.proxy.address: {{ .Values.appConfig.emailEngine.proxy.address }}
    email-engine.proxy.tenantIds: {{ .Values.appConfig.emailEngine.proxy.tenantIds }}