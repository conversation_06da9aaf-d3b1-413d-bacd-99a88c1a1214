apiVersion: v1
kind: Service
metadata:
  name: {{ include "sd-iam.fullname" . }}
  namespace: {{ .Values.namespace }}
  labels:
{{ include "sd-iam.labels" . | indent 4 }}
    service-discovery: gateway
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.appConfig.server.port }}
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: {{ include "sd-iam.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
