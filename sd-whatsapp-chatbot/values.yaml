replicaCount: 2
namespace: sling-sales

image:
  repository: nexus.sling-dev.com:8023/sling/sd-whatsapp-chatbot
  migrationRepository: nexus.sling-dev.com:8023/sling/sd-whatsapp-chatbot/db-migration
  tag: latest
  pullPolicy: Always

imagePullSecrets:
  - name: registry-credentials
nameOverride: ""
fullnameOverride: ""

service:
  type: ClusterIP
  port: 80

deployment:
  annotations:
    buildNumber: "replace-this"

ingress:
  enabled: false
  annotations: { }
  # kubernetes.io/ingress.class: nginx
  # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths: [ ]

  tls: [ ]
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources: { }
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
#   memory: 128Mi
# requests:
#   cpu: 100m
#   memory: 128Mi

nodeSelector:
  service: sd-microservices-label

tolerations: [ ]

affinity:
  nodeAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 75
        preference:
          matchExpressions:
            - key: highMemory
              operator: In
              values:
                - sd-high-memory-service-pods
      - weight: 50
        preference:
          matchExpressions:
            - key: service
              operator: In
              values:
                - sd-microservices-label
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchExpressions:
            - key: app.kubernetes.io/instance
              operator: In
              values:
                - sd-whatsapp-chatbot
        topologyKey: "kubernetes.io/hostname"

rollingUpdate:
  maxSurge: 1
  maxUnavailable: 0

appConfig:
  environment: qa
  openai:
    apikey: testKey
  gemini:
    apikey: testKey
  activeProfile: QA
  server:
    port: 8000
  postgres:
    hostname: postgresql-postgresql
    username: sling_sales
    password: test
    port: 5432
    flags: ""
    dbName: whatsapp_chatbot
  redis:
    host: redis://redis-master:6379/2
  rabbitmq:
    host: rabbitmq
    port: 5672
    virtualHost: /
    username: sling_sales
    password: test
    heartbeat: 300
    connectionTimeout: 30
    # Add any other RabbitMQ configuration your app needs
  client:
    iam:
      basePath: http://sd-iam
    email:
      basePath: http://sd-emails
  elasticsearch:
    url: "http://elasticsearch-vector-master:9200"
  aws:
    accessKeyId: "DO801TRQ46XF3B7HAFTH"
    secretAccessKey: "N1j979k8jceXeHUcMvjIUF5uu6ZsoDN4+B4w0Fii6/w"
    region: "sgp1"
    s3BucketName: "s3-for-local-testing-from-alpesh-laptop-delete"

  digitalOcean:
    spacesKey: "DO801TRQ46XF3B7HAFTH"
    spacesSecret: "N1j979k8jceXeHUcMvjIUF5uu6ZsoDN4+B4w0Fii6/w"
    spacesRegion: "sgp1"
    spacesBucket: "s3-for-local-testing-from-alpesh-laptop-delete"
    spacesEndpoint: "https://sgp1.digitaloceanspaces.com"