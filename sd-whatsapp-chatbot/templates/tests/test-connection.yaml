apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "sd-whatsapp-chatbot.fullname" . }}-test-connection"
  labels:
    {{- include "sd-whatsapp-chatbot.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "sd-whatsapp-chatbot.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
