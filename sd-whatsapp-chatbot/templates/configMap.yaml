apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Chart.Name }}-config
  namespace: {{ .Values.namespace }}
data:
  ENVIRONMENT: "{{ .Values.appConfig.environment }}"
  SERVER_PORT: "{{ .Values.appConfig.server.port }}"

  POSTGRES_HOST: "{{ .Values.appConfig.postgres.hostname }}"
  POSTGRES_PORT: "{{ .Values.appConfig.postgres.port }}"
  POSTGRES_USER: "{{ .Values.appConfig.postgres.username }}"
  POSTGRES_PASSWORD: "{{ .Values.appConfig.postgres.password }}"
  DATABASE_URL: "postgresql://{{ .Values.appConfig.postgres.username }}:{{ .Values.appConfig.postgres.password }}@{{ .Values.appConfig.postgres.hostname }}:{{ .Values.appConfig.postgres.port }}/{{ .Values.appConfig.postgres.dbName }}{{ .Values.appConfig.postgres.flags }}"

  # Elasticsearch settings
  ELASTICSEARCH_URL: "{{ .Values.appConfig.elasticsearch.url }}"
  ELASTICSEARCH_INDEX_PREFIX: "{{ .Values.appConfig.elasticsearch.indexPrefix }}"

  RABBITMQ_HOST: "{{ .Values.appConfig.rabbitmq.host }}"
  RABBITMQ_USERNAME: "{{ .Values.appConfig.rabbitmq.username }}"
  RABBITMQ_PASSWORD: "{{ .Values.appConfig.rabbitmq.password }}"
  RABBITMQ_VIRTUAL_HOST: "{{ .Values.appConfig.rabbitmq.virtualHost }}"
  RABBITMQ_URL: "amqp://{{ .Values.appConfig.rabbitmq.username }}:{{ .Values.appConfig.rabbitmq.password }}@{{ .Values.appConfig.rabbitmq.host }}:5672{{ .Values.appConfig.rabbitmq.virtualHost }}"

  # RabbitMQ additional settings


  REDIS_HOST: "{{ .Values.appConfig.redis.host }}"
  # Redis URL (alternative to just host)
  REDIS_URL: "redis://{{ .Values.appConfig.redis.host }}:{{ .Values.appConfig.redis.port | default "6379" }}/{{ .Values.appConfig.redis.database | default "0" }}"

  OPENAI_API_KEY: "{{ .Values.appConfig.openai.apikey }}"
  GEMINI_API_KEY: "{{ .Values.appConfig.gemini.apikey }}"

  IAM_BASE_PATH: "{{ .Values.appConfig.client.iam.basePath }}"
  EMAIL_BASE_PATH: "{{ .Values.appConfig.client.email.basePath }}"

  # AWS S3 settings
  AWS_ACCESS_KEY_ID: "{{ .Values.appConfig.aws.accessKeyId }}"
  AWS_SECRET_ACCESS_KEY: "{{ .Values.appConfig.aws.secretAccessKey }}"
  AWS_REGION: "{{ .Values.appConfig.aws.region }}"
  AWS_S3_BUCKET_NAME: "{{ .Values.appConfig.aws.s3BucketName }}"

  # DigitalOcean Spaces settings
  DO_SPACES_KEY: "{{ .Values.appConfig.digitalOcean.spacesKey }}"
  DO_SPACES_SECRET: "{{ .Values.appConfig.digitalOcean.spacesSecret }}"
  DO_SPACES_REGION: "{{ .Values.appConfig.digitalOcean.spacesRegion }}"
  DO_SPACES_BUCKET: "{{ .Values.appConfig.digitalOcean.spacesBucket }}"
  DO_SPACES_ENDPOINT: "{{ .Values.appConfig.digitalOcean.spacesEndpoint }}"

  # Service name
