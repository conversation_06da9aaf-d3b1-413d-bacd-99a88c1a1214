apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Chart.Name }}-config
  namespace: {{ .Values.namespace }}
data:
  application.properties: |
    spring.profiles.active: {{ .Values.appConfig.activeProfile }}
    server.port: {{ .Values.appConfig.server.port }}
    spring.r2dbc.url: r2dbc:pool:postgresql://{{ .Values.appConfig.postgres.hostname }}:{{ .Values.appConfig.postgres.port }}/import{{ .Values.appConfig.postgres.flags }}
    spring.r2dbc.username: {{ .Values.appConfig.postgres.username }}
    spring.r2dbc.password: {{ .Values.appConfig.postgres.password }}
    client.config.basePath: {{ .Values.appConfig.client.config.basePath }}
    client.sales.basePath: {{ .Values.appConfig.client.sales.basePath }}
    client.search.basePath: {{ .Values.appConfig.client.search.basePath }}
    client.iam.basePath: {{ .Values.appConfig.client.iam.basePath }}
    client.company.basePath: {{ .Values.appConfig.client.company.basePath }}
    client.meeting.basePath: {{ .Values.appConfig.client.meeting.basePath }}
    client.deal.basePath: {{ .Values.appConfig.client.deal.basePath }}
    client.productivity.basePath: {{ .Values.appConfig.client.productivity.basePath }}
    spring.rabbitmq.host: {{ .Values.appConfig.rabbitmq.host }}
    spring.rabbitmq.username: {{ .Values.appConfig.rabbitmq.username }}
    spring.rabbitmq.password: {{ .Values.appConfig.rabbitmq.password }}
    spring.rabbitmq.virtualHost: {{ .Values.appConfig.rabbitmq.virtualHost }}
    s3.endpoint: {{ .Values.appConfig.s3.endpoint }}
    s3.region: {{ .Values.appConfig.s3.region }}
    s3.key: {{ .Values.appConfig.s3.key }}
    s3.secret: {{ .Values.appConfig.s3.secret }}
    s3.bucket-name: {{ .Values.appConfig.s3.bucketName }}
    s3.lead.endpoint: {{ .Values.appConfig.s3.lead.endpoint }}
    s3.lead.region: {{ .Values.appConfig.s3.lead.region }}
    s3.lead.key: {{ .Values.appConfig.s3.lead.key }}
    s3.lead.secret: {{ .Values.appConfig.s3.lead.secret }}
    s3.lead.bucket-name: {{ .Values.appConfig.s3.lead.bucketName }}
    s3.contact.endpoint: {{ .Values.appConfig.s3.contact.endpoint }}
    s3.contact.region: {{ .Values.appConfig.s3.contact.region }}
    s3.contact.key: {{ .Values.appConfig.s3.contact.key }}
    s3.contact.secret: {{ .Values.appConfig.s3.contact.secret }}
    s3.contact.bucket-name: {{ .Values.appConfig.s3.contact.bucketName }}
    s3.company.endpoint: {{ .Values.appConfig.s3.company.endpoint }}
    s3.company.region: {{ .Values.appConfig.s3.company.region }}
    s3.company.key: {{ .Values.appConfig.s3.company.key }}
    s3.company.secret: {{ .Values.appConfig.s3.company.secret }}
    s3.company.bucket-name: {{ .Values.appConfig.s3.company.bucketName }}
    s3.meeting.endpoint: {{ .Values.appConfig.s3.meeting.endpoint }}
    s3.meeting.region: {{ .Values.appConfig.s3.meeting.region }}
    s3.meeting.key: {{ .Values.appConfig.s3.meeting.key }}
    s3.meeting.secret: {{ .Values.appConfig.s3.meeting.secret }}
    s3.meeting.bucket-name: {{ .Values.appConfig.s3.meeting.bucketName }}
    s3.deal.endpoint: {{ .Values.appConfig.s3.deal.endpoint }}
    s3.deal.region: {{ .Values.appConfig.s3.deal.region }}
    s3.deal.key: {{ .Values.appConfig.s3.deal.key }}
    s3.deal.secret: {{ .Values.appConfig.s3.deal.secret }}
    s3.deal.bucket-name: {{ .Values.appConfig.s3.deal.bucketName }}
    s3.task.endpoint: {{ .Values.appConfig.s3.task.endpoint }}
    s3.task.region: {{ .Values.appConfig.s3.task.region }}
    s3.task.key: {{ .Values.appConfig.s3.task.key }}
    s3.task.secret: {{ .Values.appConfig.s3.task.secret }}
    s3.task.bucket-name: {{ .Values.appConfig.s3.task.bucketName }}
    
