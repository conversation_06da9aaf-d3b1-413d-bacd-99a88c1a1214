apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Chart.Name }}-config
  namespace: {{ .Values.namespace }}
data:
  application.properties: |
    server.port: {{ .Values.appConfig.server.port }}
    spring.profiles.active={{ .Values.appConfig.activeProfile }}
    core.rabbitmq.host: {{ .Values.appConfig.rabbitmq.host }}
    core.rabbitmq.username: {{ .Values.appConfig.rabbitmq.username }}
    core.rabbitmq.password: {{ .Values.appConfig.rabbitmq.password }}
    core.rabbitmq.virtualHost: {{ .Values.appConfig.rabbitmq.virtualHost }}
    spring.datasource.url: jdbc:postgresql://{{ .Values.appConfig.postgres.hostname }}:{{ .Values.appConfig.postgres.port }}/search{{ .Values.appConfig.postgres.flags }}
    spring.datasource.username: {{ .Values.appConfig.postgres.username }}
    spring.datasource.password: {{ .Values.appConfig.postgres.password }}
    spring.datasource.hikari.maximum-pool-size: {{ .Values.appConfig.datasource.hikari.maximumPoolSize }}
    client.iam.basePath: {{ .Values.appConfig.client.iam.basePath }}
    client.entity.basePath: {{ .Values.appConfig.client.entity.basePath }}
    client.meeting.basePath: {{ .Values.appConfig.client.meeting.basePath }}
    client.company.basePath: {{ .Values.appConfig.client.company.basePath }}
    client.deal.basePath: {{ .Values.appConfig.client.deal.basePath }}
    newElasticsearch.host: {{ .Values.appConfig.newElasticsearch.host }}
    newElasticsearch.port: {{ .Values.appConfig.newElasticsearch.port }}
    memcached.host: {{ .Values.appConfig.memcached.host }}
    logging.level.root: WARN
    logging.level.org.springframework.web: WARN
    logging.level.org.hibernate: ERROR
    flyway.enabled: false
    
