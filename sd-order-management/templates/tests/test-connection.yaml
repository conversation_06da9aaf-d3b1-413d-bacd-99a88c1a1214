apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "sd-order-management.fullname" . }}-test-connection"
  labels:
    {{- include "sd-order-management.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "sd-order-management.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
