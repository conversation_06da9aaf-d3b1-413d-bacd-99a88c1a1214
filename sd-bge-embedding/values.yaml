# Default values for bge-embedding chart

replicaCount: 1

image:
  repository: nexus.sling-dev.com:8023/embedding/bge-embedding
  pullPolicy: IfNotPresent
  tag: "v1"

# Override Docker ENTRYPOINT and CMD
container:
  # Override the ENTRYPOINT from Dockerfile
  command: ["uvicorn"]
  args:
    - "embedding_api:app"
    - "--host"
    - "0.0.0.0"
    - "--port"
    - "8000"
    - "--workers"
    - "4"
    - "--log-level"
    - "info"

# Alternative configurations for different scenarios:

# For debugging (override to keep container running):
# container:
#   command: ["/bin/bash"]
#   args: ["-c", "sleep infinity"]

# For running with Python module:
# container:
#   command: ["python"]
#   args: ["-m", "embedding_api"]

# For custom startup script:
# container:
#   command: ["/bin/bash"]
#   args: ["/app/startup.sh"]

# Environment variables
env:
  - name: HF_HUB_OFFLINE
    value: "1"
  - name: TRANSFORMERS_OFFLINE
    value: "1"
  - name: HF_DATASETS_OFFLINE
    value: "1"

imagePullSecrets:
  - name: registry-credentials
    
# Service configuration
service:
  type: ClusterIP
  port: 8000
  targetPort: 8000



# Resource limits and requests
resources:
  limits:
    cpu: 2000m
    memory: 4Gi
  requests:
    cpu: 1000m
    memory: 2Gi

# Health checks
livenessProbe:
  enabled: true
  path: /
  initialDelaySeconds: 60
  periodSeconds: 30
  timeoutSeconds: 10
  failureThreshold: 3

readinessProbe:
  enabled: true
  path: /
  initialDelaySeconds: 30
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 3

# Node selector, tolerations, and affinity
nodeSelector: {}
tolerations: []
affinity: {}


