apiVersion: v1
kind: Service
metadata:
  name: {{ include "sd-bge-embedding.fullname" . }}
  labels:
    {{- include "sd-bge-embedding.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
      protocol: TCP
      name: http
  selector:
    {{- include "sd-bge-embedding.selectorLabels" . | nindent 4 }}
