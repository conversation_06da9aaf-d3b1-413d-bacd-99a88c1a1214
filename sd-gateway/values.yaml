# Default values for sd-gateway.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1
namespace: sling-sales

image:
  repository: nexus.sling-dev.com:8023/sling/sd-gateway
  tag: latest
  pullPolicy: Always

imagePullSecrets:
  - name: registry-credentials
nameOverride: ""
fullnameOverride: ""

deployment:
  annotations:
    buildNumber: "replace-this"

service:
  type: LoadBalancer
  port: 80
  sslPort: 443
  certificateId: "N/A"

ingress:
  enabled: false
  annotations:
    {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths: []

  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
   requests:
#     cpu: 100m
     memory: 900Mi
autoscaling:
  enabled: false
  minReplicas: 2
  maxReplicas: 8
  #    targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

affinity:
  nodeAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 75
        preference:
          matchExpressions:
            - key: highMemory
              operator: In
              values:
                - sd-high-memory-service-pods
      - weight: 50
        preference:
          matchExpressions:
            - key: service
              operator: In
              values:
                - sd-microservices-label
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchExpressions:
            - key: app.kubernetes.io/instance
              operator: In
              values:
                - sd-gateway
        topologyKey: "kubernetes.io/hostname"

rollingUpdate:
  maxSurge: 1
  maxUnavailable: 0

appConfig:
  activeProfile: QA
  server:
    port: 8080
  iamService: sd-iam
  redis:
    host: redis-master
    port: 6379
    password: ""
    namespace: qa
    failedRequestThreshold: 100
    blockDuration: 20
    useSsl: false