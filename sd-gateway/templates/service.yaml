apiVersion: v1
kind: Service
metadata:
  name: {{ include "sd-gateway.fullname" . }}
  labels:
{{ include "sd-gateway.labels" . | indent 4 }}
  namespace: {{ .Values.namespace }}
  {{ if ne .Values.service.certificateId "N/A" }}
  annotations:
    service.beta.kubernetes.io/do-loadbalancer-protocol: "http"
    service.beta.kubernetes.io/do-loadbalancer-algorithm: "round_robin"
    service.beta.kubernetes.io/do-loadbalancer-tls-ports: "443"
    service.beta.kubernetes.io/do-loadbalancer-certificate-id: {{ .Values.service.certificateId }}
    service.beta.kubernetes.io/do-loadbalancer-redirect-http-to-https: "true"
  {{ end }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - name: http
      protocol: TCP
      port: {{ .Values.service.port }}
      targetPort: {{ .Values.appConfig.server.port }}
    {{ if ne .Values.service.certificateId "N/A" }}
    - name: https
      protocol: TCP
      port: {{ .Values.service.sslPort }}
      targetPort: {{ .Values.appConfig.server.port }}
    {{ end }}
  selector:
    app.kubernetes.io/name: {{ include "sd-gateway.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
