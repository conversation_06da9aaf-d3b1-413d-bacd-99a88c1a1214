apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Chart.Name }}-config
  namespace: {{ .Values.namespace }}
data:
  application.yml: |-
    spring:
      profiles:
        active : {{ .Values.appConfig.activeProfile }}
      cloud:
        gateway:
          globalcors:
            corsConfigurations:
              '[/**]':
                allowedOrigins: "*"
                allowedHeaders: "*"
                allowedMethods:
                  - GET
                  - POST
                  - PUT
                  - PATCH
                  - DELETE

        kubernetes:
          client:
            namespace : {{ .Values.namespace }}
          discovery:
            serviceLabels :
              service-discovery: gateway
            locator:
              enabled: true
              url-expression: "'http://'+serviceId"
    iam-service: {{ .Values.appConfig.iamService }}
    redis:
      host: {{ .Values.appConfig.redis.host }}
      port: {{ .Values.appConfig.redis.port }}
      password: {{ .Values.appConfig.redis.password }}
      useSsl: {{ .Values.appConfig.redis.useSsl }}
      namespace: {{ .Values.appConfig.redis.namespace }}
      failedRequestThreshold: {{ .Values.appConfig.redis.failedRequestThreshold }}
      blockDuration: {{ .Values.appConfig.redis.blockDuration }}
    amura:
      cloud:
        gateway:
          tokenUrl : http://sd-iam
          services :
            - sd-iam
            - sd-sales
            - sd-productivity
            - sd-config
            - sd-search
            - sd-import
            - sd-product
            - sd-deal
            - sd-company
            - sd-report
            - sd-meetings
            - sd-emails
            - sd-workflow
            - sd-call
            - sd-marketplace
            - sd-message
            - sd-notification
            - sd-dashboard
            - sd-bulk-actions
            - sd-export
            - sd-webhook
            - sd-order-management
            - sd-document
            - sd-feed
            - sd-forex
            - sd-data-migrations
            - sd-field-sales
            - sd-score-rule
            - sd-ai
            - sd-campaign
            - sd-whatsapp-chatbot

    
