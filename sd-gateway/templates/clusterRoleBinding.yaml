---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: fabric8-admin
  namespace: {{ .Values.namespace }}
rules:
  - apiGroups: [""]
    resources: ["pods", "services", "configmaps", "endpoints"]
    verbs: ["get", "watch", "list"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: fabric8-rbac
  namespace: {{ .Values.namespace }}
subjects:
  - kind: ServiceAccount
    name: default
roleRef:
  kind: Role
  name: fabric8-admin
  apiGroup: rbac.authorization.k8s.io
