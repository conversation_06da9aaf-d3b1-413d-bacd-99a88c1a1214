# Default values for sd-company.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 2
namespace: sling-sales

image:
  repository: nexus.sling-dev.com:8023/sling/sd-forex
  migrationRepository: nexus.sling-dev.com:8023/sling/sd-forex/db-migration
  tag: latest
  pullPolicy: Always

imagePullSecrets:
  - name: registry-credentials
nameOverride: ""
fullnameOverride: ""

service:
  type: ClusterIP
  port: 80

deployment:
  annotations:
    buildNumber: "replace-this"

ingress:
  enabled: false
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths: []

  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

nodeSelector: {}

tolerations: []

affinity:
  nodeAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 75
        preference:
          matchExpressions:
            - key: highMemory
              operator: In
              values:
                - sd-high-memory-service-pods
      - weight: 50
        preference:
          matchExpressions:
            - key: service
              operator: In
              values:
                - sd-microservices-label
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchExpressions:
            - key: app.kubernetes.io/instance
              operator: In
              values:
                - sd-forex
        topologyKey: "kubernetes.io/hostname"

rollingUpdate:
  maxSurge: 1
  maxUnavailable: 0

appConfig:
  activeProfile: QA
  server:
    port: 8080
  postgres:
    hostname: postgresql-postgresql
    username: sling_sales
    password: test
    port: 5432
    flags: ""
  rabbitmq:
    host: rabbitmq
    virtualHost: /
    username: sling_sales
    password: test
  client:
    iam:
      basePath: http://sd-iam
    sales:
      basePath: http://sd-sales
  baseUrl: https://localhost:9091
