apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Chart.Name }}-config
  namespace: {{ .Values.namespace }}
data:
  application.properties: |
    spring.profiles.active={{ .Values.appConfig.activeProfile }}
    server.port: {{ .Values.appConfig.server.port }}
    core.rabbitmq.host: {{ .Values.appConfig.rabbitmq.host }}
    core.rabbitmq.username: {{ .Values.appConfig.rabbitmq.username }}
    core.rabbitmq.password: {{ .Values.appConfig.rabbitmq.password }}
    core.rabbitmq.virtualHost: {{ .Values.appConfig.rabbitmq.virtualHost }}
    spring.datasource.url: jdbc:postgresql://{{ .Values.appConfig.postgres.hostname }}:{{ .Values.appConfig.postgres.port }}/sales{{ .Values.appConfig.postgres.flags }}
    spring.datasource.username: {{ .Values.appConfig.postgres.username }}
    spring.datasource.password: {{ .Values.appConfig.postgres.password }}
    client.iam.basePath: {{ .Values.appConfig.client.iam.basePath }}
    client.entity.basePath: {{ .Values.appConfig.client.entity.basePath }}
    client.search.basePath: {{ .Values.appConfig.client.search.basePath }}
    client.product.basePath: {{ .Values.appConfig.client.product.basePath }}
    client.company.basePath: {{ .Values.appConfig.client.company.basePath }}
    client.deal.basePath: {{ .Values.appConfig.client.deal.basePath }}
    client.campaign.basePath: {{ .Values.appConfig.client.campaign.basePath }}
    elasticsearch.host: {{ .Values.appConfig.elasticsearch.host }}
    elasticsearch.port: {{ .Values.appConfig.elasticsearch.port }}
    newElasticsearch.host: {{ .Values.appConfig.newElasticsearch.host }}
    newElasticsearch.port: {{ .Values.appConfig.newElasticsearch.port }}
    spring.datasource.hikari.maximum-pool-size: {{ .Values.appConfig.spring.datasource.hikari.maximumPoolSize }}
    spring.datasource.hikari.minimum-idle: {{ .Values.appConfig.spring.datasource.hikari.minimumIdle }}
    spring.datasource.hikari.idle-timeout: {{ .Values.appConfig.spring.datasource.hikari.idleTimeout }}
    spring.datasource.hikari.pool-name: {{ .Values.appConfig.spring.datasource.hikari.poolName }}
    spring.datasource.hikari.connection-timeout: {{ .Values.appConfig.spring.datasource.hikari.connectionTimeout }}
    spring.datasource.hikari.leak-detection-threshold: {{ .Values.appConfig.spring.datasource.hikari.leakDetectionThreshold }}
    management.health.db.enabled: {{ .Values.appConfig.actuator.health.db.enabled }}
    s3.endpoint: {{ .Values.appConfig.s3.endpoint }}
    s3.region: {{ .Values.appConfig.s3.region }}
    s3.key: {{ .Values.appConfig.s3.key }}
    s3.secret: {{ .Values.appConfig.s3.secret }}
    s3.lead-capture-form-folder: {{ .Values.appConfig.s3.leadCaptureFormFolder }}
    s3.assets-bucket-name: {{ .Values.appConfig.s3.assetsBucketName}}
    memcached.host: {{ .Values.appConfig.memcached.host }}
    flyway.enabled: false
