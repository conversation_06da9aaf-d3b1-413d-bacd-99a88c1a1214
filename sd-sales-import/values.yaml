# Default values for sd-sales.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 2
namespace: sling-sales

image:
  repository: nexus.sling-dev.com:8023/sling/sd-sales
  tag: latest
  pullPolicy: Always

imagePullSecrets:
  - name: registry-credentials
nameOverride: ""
fullnameOverride: ""

deployment:
  annotations:
    buildNumber: "replace-this"

service:
  type: ClusterIP
  port: 80

ingress:
  enabled: false
  annotations: {}
    # kubernetes.io/ingress.class: nginx
  # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths: []

  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
#   memory: 128Mi

nodeSelector: {}

tolerations: []

affinity: {}

rollingUpdate:
  maxSurge: 1
  maxUnavailable: 0

client:
  iam:
    basePath: http://sd-iam
  entity:
    basePath: http://sd-config

elasticsearch:
  host: elasticsearch-master
  port: 9200
newElasticsearch:
  host: new-elasticsearch-master
  port: 9201

appConfig:
  server:
    port: 8080
  rabbitmq:
    host: rabbitmq
    virtualHost: /
    username: sling_sales
    password: test
  postgres:
    hostname: postgresql-postgresql
    username: sling_sales
    password: test
  elasticsearch:
    host: elasticsearch-master
    port: 9200
  newElasticsearch:
    host: new-elasticsearch-master
    port: 9201
  client:
    iam:
      basePath: http://sd-iam
    entity:
      basePath: http://sd-config
    search:
      basePath: http://sd-search
    product:
      basePath: http://sd-product
  spring:
    datasource:
      hikari:
        maximumPoolSize: 15
        minimumIdle: 5
        idleTimeout: 30000
        poolName: SpringBootJPAHikariCP
        connectionTimeout: 30000
        maxLifetime: 30000
  actuator:
    health:
      db:
        enabled: false
