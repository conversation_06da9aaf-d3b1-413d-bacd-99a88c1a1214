apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "sd-sales-import.fullname" . }}-test-connection"
  labels:
{{ include "sd-sales-import.labels" . | indent 4 }}
  annotations:
    "helm.sh/hook": test-success
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args:  ['{{ include "sd-sales-import.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
