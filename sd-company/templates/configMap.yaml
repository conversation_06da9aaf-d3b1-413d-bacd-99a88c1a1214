apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Chart.Name }}-config
  namespace: {{ .Values.namespace }}
data:
  application.yml: |-
    spring:
      profiles:
        active : {{ .Values.appConfig.activeProfile }}
      datasource:
        url: jdbc:postgresql://{{ .Values.appConfig.postgres.hostname }}:{{ .Values.appConfig.postgres.port }}/company{{ .Values.appConfig.postgres.flags }}
        username: {{ .Values.appConfig.postgres.username }}
        password: {{ .Values.appConfig.postgres.password }}
        hikari:
          maximum-pool-size: {{ .Values.appConfig.postgres.hikari.maximumPoolSize }}
          pool-name: SpringBootJPAHikariCP
          minimum-idle: 5
          idle-timeout: 60000
          max-lifetime: 1800000
      rabbitmq:
        host: {{ .Values.appConfig.rabbitmq.host }}
        username: {{ .Values.appConfig.rabbitmq.username }}
        password: {{ .Values.appConfig.rabbitmq.password }}
        virtual-host: {{ .Values.appConfig.rabbitmq.virtualHost }}
    server:
      port: {{ .Values.appConfig.server.port }}
    client:
         config:
           basePath: {{ .Values.appConfig.client.config.basePath }}
         iam:
           basePath: {{ .Values.appConfig.client.iam.basePath }}
    s3:
      endpoint: {{ .Values.appConfig.s3.endpoint }}
      region: {{ .Values.appConfig.s3.region }}
      key: {{ .Values.appConfig.s3.key }}
      secret: {{ .Values.appConfig.s3.secret }}
      bucket-name: {{ .Values.appConfig.s3.bucketName }}
    memcached:
      host: {{ .Values.appConfig.memcached.host }}