apiVersion: apps/v1
kind: Deployment
metadata:
  name: sd-meetings-sidekiq
  labels: 
{{ include "sd-meetings-sidekiq.labels" . | indent 4 }}
  namespace: {{ .Values.namespace }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: sd-meetings-sidekiq
      app.kubernetes.io/instance: sd-meetings-sidekiq
  template:
    metadata:
      labels:
        app.kubernetes.io/name: sd-meetings-sidekiq
        app.kubernetes.io/instance: sd-meetings-sidekiq
        buildNumber: {{ .Values.deployment.annotations.buildNumber | quote }}
    spec:
    {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
    {{- end }}
      containers:
        - name: "{{ .Chart.Name }}-sidekiq"
          image: "{{ .Values.image.sidekiqRepository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
          - name: DATABASE_HOST
            value: {{ .Values.appConfig.postgres.hostname }}
          - name: DATABASE_PORT
            value: {{ .Values.appConfig.postgres.port  | quote }}
          - name: DATABASE_USER
            value: {{ .Values.appConfig.postgres.username }}
          - name: DATABASE_PASSWORD
            value: {{ .Values.appConfig.postgres.password }}
          - name: DATABASE_NAME
            value: {{ .Values.appConfig.postgres.dbName }}
          - name: DATABASE_POOL
            value: {{ .Values.appConfig.postgres.sidekiqPoolSize | quote }}
          - name: RAILS_MASTER_KEY
            value: {{ .Values.appConfig.credentials.masterKey }}
          - name: RABBITMQ_USER
            value: {{ .Values.appConfig.rabbitmq.username }}
          - name: RABBITMQ_PASSWORD
            value: {{ .Values.appConfig.rabbitmq.password }}
          - name: RABBITMQ_VIRTUAL_HOST
            value: {{ .Values.appConfig.rabbitmq.virtualHost }}
          - name: RABBITMQ_HOST
            value: {{ .Values.appConfig.rabbitmq.host }}
          - name: RAILS_LOG_TO_STDOUT
            value: {{ .Values.appConfig.logoutput | quote }}
          - name: REDIS_HOST
            value: {{ .Values.appConfig.redis.host }}
          - name: API_KYLAS_HOST
            value: {{ .Values.appConfig.kylas.apiHost | quote }}
          - name: CALENDAR_CREDENTIAL_ENCRYPTION_SECRET
            value: {{ .Values.appConfig.calendar.credential.encryption.secret | quote }}
          - name: CALENDAR_CREDENTIAL_ENCRYPTION_IV
            value: {{ .Values.appConfig.calendar.credential.encryption.iv | quote }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}