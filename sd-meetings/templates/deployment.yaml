apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "sd-meetings.fullname" . }}
  labels:
{{ include "sd-meetings.labels" . | indent 4 }}
  namespace: {{ .Values.namespace }}
spec:
  replicas: {{ .Values.replicaCount }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: {{ .Values.rollingUpdate.maxSurge }}
      maxUnavailable: {{ .Values.rollingUpdate.maxUnavailable }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "sd-meetings.name" . }}
      app.kubernetes.io/instance: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app.kubernetes.io/name: {{ include "sd-meetings.name" . }}
        app.kubernetes.io/instance: {{ .Release.Name }}
        buildNumber: {{ .Values.deployment.annotations.buildNumber | quote }}
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "{{ .Values.appConfig.server.port }}"
        prometheus.io/path: "/prometheus"
        prometheus.io/scheme: "http"
    spec:
    {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
    {{- end }}
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: service-port
              containerPort: {{ .Values.appConfig.server.port }}
          livenessProbe:
            httpGet:
              path: /health
              port: service-port
            initialDelaySeconds: 60
            periodSeconds: 30
            timeoutSeconds: 10
          readinessProbe:
            httpGet:
              path: /health
              port: service-port
            initialDelaySeconds: 60
            periodSeconds: 30
            timeoutSeconds: 10
          env:
          - name: DATABASE_HOST
            value: {{ .Values.appConfig.postgres.hostname }}
          - name: DATABASE_PORT
            value: {{ .Values.appConfig.postgres.port  | quote }}
          - name: DATABASE_USER
            value: {{ .Values.appConfig.postgres.username }}
          - name: DATABASE_PASSWORD
            value: {{ .Values.appConfig.postgres.password }}
          - name: DATABASE_NAME
            value: {{ .Values.appConfig.postgres.dbName }}
          - name: RAILS_MASTER_KEY
            value: {{ .Values.appConfig.credentials.masterKey }}
          - name: RABBITMQ_USER
            value: {{ .Values.appConfig.rabbitmq.username }}
          - name: RABBITMQ_PASSWORD
            value: {{ .Values.appConfig.rabbitmq.password }}
          - name: RABBITMQ_VIRTUAL_HOST
            value: {{ .Values.appConfig.rabbitmq.virtualHost }}
          - name: RABBITMQ_HOST
            value: {{ .Values.appConfig.rabbitmq.host }}
          - name: RAILS_LOG_TO_STDOUT
            value: {{ .Values.appConfig.logoutput | quote }}
          - name: SALES_SERVICE_HOST
            value: {{ .Values.appConfig.client.sales.basePath | quote }}
          - name: DEAL_SERVICE_HOST
            value: {{ .Values.appConfig.client.deal.basePath | quote  }}
          - name: IAM_SERVICE_HOST
            value: {{ .Values.appConfig.client.iam.basePath | quote  }}
          - name: APP_KYLAS_HOST
            value: {{ .Values.appConfig.kylas.uiHost | quote }}
          - name: API_KYLAS_HOST
            value: {{ .Values.appConfig.kylas.apiHost | quote }}
          - name: CALENDAR_CREDENTIAL_ENCRYPTION_SECRET
            value: {{ .Values.appConfig.calendar.credential.encryption.secret | quote }}
          - name: CALENDAR_CREDENTIAL_ENCRYPTION_IV
            value: {{ .Values.appConfig.calendar.credential.encryption.iv | quote }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
