apiVersion: batch/v1
kind: Job
metadata:
  name: {{ include "sd-meetings.fullname" . }}
  labels:
{{ include "sd-meetings.labels" . | indent 4 }}
  namespace: {{ .Values.namespace }}
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade
    "helm.sh/hook-weight": "1"
    "helm.sh/hook-delete-policy": before-hook-creation
spec:
  template:
    spec:
    {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
    {{- end }}
      containers:
        - name: "{{ .Chart.Name }}-db-migration"
          image: "{{ .Values.image.migrationRepository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
            - name: RAILS_MASTER_KEY
              value: {{ .Values.appConfig.credentials.masterKey }}
            - name: DATABASE_HOST
              value: {{ .Values.appConfig.postgres.hostname | quote }}
            - name: DATABASE_PORT
              value: {{ .Values.appConfig.postgres.port  | quote }}
            - name: DATABASE_USER
              value: {{ .Values.appConfig.postgres.username }}
            - name: DATABASE_PASSWORD
              value: {{ .Values.appConfig.postgres.password }}
            - name: DATABASE_NAME
              value: {{ .Values.appConfig.postgres.dbName }}
            - name: RABBITMQ_USER
              value: {{ .Values.appConfig.rabbitmq.username }}
            - name: RABBITMQ_PASSWORD
              value: {{ .Values.appConfig.rabbitmq.password }}
            - name: RABBITMQ_VIRTUAL_HOST
              value: {{ .Values.appConfig.rabbitmq.virtualHost }}
            - name: RABBITMQ_HOST
              value: {{ .Values.appConfig.rabbitmq.host }}
          args:
            - migrate
      restartPolicy: Never
