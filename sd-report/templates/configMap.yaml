apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Chart.Name }}-config
  namespace: {{ .Values.namespace }}
data:
  application.yml: |-
    spring:
      profiles:
        active : {{ .Values.appConfig.activeProfile }}
      datasource:
        url: jdbc:postgresql://{{ .Values.appConfig.postgres.hostname }}:{{ .Values.appConfig.postgres.port }}/report{{ .Values.appConfig.postgres.flags }}
        username: {{ .Values.appConfig.postgres.username }}
        password: {{ .Values.appConfig.postgres.password }}
        hikari:
          maximum-pool-size: {{ .Values.appConfig.spring.datasource.hikari.maximumPoolSize }}
      rabbitmq:
        host: {{ .Values.appConfig.rabbitmq.host }}
        username: {{ .Values.appConfig.rabbitmq.username }}
        password: {{ .Values.appConfig.rabbitmq.password }}
        virtual-host: {{ .Values.appConfig.rabbitmq.virtualHost }}
    server:
      port: {{ .Values.appConfig.server.port }}
    client:
      search:
        basePath: {{ .Values.appConfig.client.search.basePath }}
      config:
        basePath: {{ .Values.appConfig.client.config.basePath }}
      iam:
        basePath: {{ .Values.appConfig.client.iam.basePath }}
      sales:
        basePath: {{ .Values.appConfig.client.sales.basePath }}
      deal:
        basePath: {{ .Values.appConfig.client.deal.basePath }}
      company:
        basePath: {{ .Values.appConfig.client.company.basePath }}
      meeting:
        basePath: {{ .Values.appConfig.client.meeting.basePath }}
      call:
        basePath: {{ .Values.appConfig.client.call.basePath }}
      email:
        basePath: {{ .Values.appConfig.client.email.basePath }}
    memcached:
      host: {{ .Values.appConfig.memcached.host }}
