# Default values for sd-company.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1
namespace: sling-sales

image:
  repository: nexus.sling-dev.com:8023/sling/sd-scheduler
  tag: latest
  pullPolicy: Always

imagePullSecrets:
  - name: registry-credentials
nameOverride: ""
fullnameOverride: ""

service:
  type: ClusterIP
  port: 80

deployment:
  annotations:
    buildNumber: "replace-this"

ingress:
  enabled: false
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths: []

  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

nodeSelector:
  service: sd-microservices-label

tolerations: []

affinity: {}

rollingUpdate:
  maxSurge: 1
  maxUnavailable: 0

appConfig:
  activeProfile: qa
  server:
    port: 8080
  rabbitmq:
    host: rabbitmq
    virtualHost: /
    username: sling_sales
    password: test
