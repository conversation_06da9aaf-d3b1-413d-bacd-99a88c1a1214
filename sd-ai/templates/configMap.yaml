apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Chart.Name }}-config
  namespace: {{ .Values.namespace }}
data:
  ENVIRONMENT: "{{ .Values.appConfig.environment }}"
  SERVER_PORT: "{{ .Values.appConfig.server.port }}"

  POSTGRES_HOST: "{{ .Values.appConfig.postgres.hostname }}"
  POSTGRES_PORT: "{{ .Values.appConfig.postgres.port }}"
  POSTGRES_USER: "{{ .Values.appConfig.postgres.username }}"
  POSTGRES_PASSWORD: "{{ .Values.appConfig.postgres.password }}"
  DATABASE_URL: "postgresql://{{ .Values.appConfig.postgres.username }}:{{ .Values.appConfig.postgres.password }}@{{ .Values.appConfig.postgres.hostname }}:{{ .Values.appConfig.postgres.port }}/{{ .Values.appConfig.postgres.dbName }}{{ .Values.appConfig.postgres.flags }}"


  RABBITMQ_HOST: "{{ .Values.appConfig.rabbitmq.host }}"
  RABBITMQ_USERNAME: "{{ .Values.appConfig.rabbitmq.username }}"
  RABBITMQ_PASSWORD: "{{ .Values.appConfig.rabbitmq.password }}"
  RABBITMQ_VIRTUAL_HOST: "{{ .Values.appConfig.rabbitmq.virtualHost }}"
  RABBITMQ_URL: "amqp://{{ .Values.appConfig.rabbitmq.username }}:{{ .Values.appConfig.rabbitmq.password }}@{{ .Values.appConfig.rabbitmq.host }}:5672{{ .Values.appConfig.rabbitmq.virtualHost }}"

  PINECONE_API_KEY: "{{ .Values.appConfig.pinecone.apikey }}"
  PINECONE_INDEX_NAME: "{{ .Values.appConfig.pinecone.indexName }}"
  PINECONE_INDEX_HOST: "{{ .Values.appConfig.pinecone.indexHost }}"

  REDIS_HOST: "{{ .Values.appConfig.redis.host }}"

  OPENAI_API_KEY: "{{ .Values.appConfig.openai.apikey }}"
  GEMINI_API_KEY: "{{ .Values.appConfig.gemini.apikey }}"

  IAM_BASE_PATH: "{{ .Values.appConfig.client.iam.basePath }}"
  EMAIL_BASE_PATH: "{{ .Values.appConfig.client.email.basePath }}"
