apiVersion: batch/v1
kind: Job
metadata:
  name: {{ include "sd-ai.fullname" . }}-db-migration
  labels:
    {{- include "sd-ai.labels" . | nindent 4 }}
  namespace: {{ .Values.namespace }}
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade
    "helm.sh/hook-weight": "1"
    "helm.sh/hook-delete-policy": before-hook-creation
spec:
  template:
    spec:
      restartPolicy: Never
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
        - name: "{{ .Chart.Name }}-db-migration"
          image: "{{ .Values.image.migrationRepository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
            - name: POSTGRES_USER
              value: {{ .Values.appConfig.postgres.username }}
            - name: POSTGRES_PASSWORD
              value: {{ .Values.appConfig.postgres.password }}
            - name: POSTGRES_HOST
              value: "{{ .Values.appConfig.postgres.hostname }}:{{ .Values.appConfig.postgres.port }}"
            - name: POSTGRES_PORT
              value: "{{ .Values.appConfig.postgres.port }}"
            - name: POSTGRES_DB
              value: {{ .Values.appConfig.postgres.dbName }}
          command: ["sh", "-c", "alembic upgrade head"]
