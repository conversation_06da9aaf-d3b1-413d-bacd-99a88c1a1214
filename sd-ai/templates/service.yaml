apiVersion: v1
kind: Service
metadata:
  name: {{ include "sd-ai.fullname" . }}
  labels:
    {{ include "sd-ai.labels" . | nindent 4 }}
    service-discovery: gateway
  namespace: {{ .Values.namespace }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.appConfig.server.port }}
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: {{ include "sd-ai.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
