replicaCount: 2
namespace: sling-sales

image:
  repository: nexus.sling-dev.com:8023/sling/sd-ai
  migrationRepository: nexus.sling-dev.com:8023/sling/sd-ai/db-migration
  tag: latest
  pullPolicy: Always

imagePullSecrets:
  - name: registry-credentials
nameOverride: ""
fullnameOverride: ""

service:
  type: ClusterIP
  port: 80

deployment:
  annotations:
    buildNumber: "replace-this"

ingress:
  enabled: false
  annotations: { }
  # kubernetes.io/ingress.class: nginx
  # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths: [ ]

  tls: [ ]
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources:
  limits:
    memory: 1500Mi
  requests:
    memory: 512Mi
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.


nodeSelector:
  service: sd-microservices-label

tolerations: [ ]

affinity:
  nodeAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 75
        preference:
          matchExpressions:
            - key: highMemory
              operator: In
              values:
                - sd-high-memory-service-pods
      - weight: 50
        preference:
          matchExpressions:
            - key: service
              operator: In
              values:
                - sd-microservices-label
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchExpressions:
            - key: app.kubernetes.io/instance
              operator: In
              values:
                - sd-ai
        topologyKey: "kubernetes.io/hostname"

rollingUpdate:
  maxSurge: 1
  maxUnavailable: 0

appConfig:
  environment: qa
  openai:
    apikey: testKey
  gemini:
    apikey: testKey
  pinecone:
    apikey: pcsk_3zdmJJ_D1DrmwKM68vQxCGUexXTz6VdyQ5XEnVW26nXNDEtBd4nwmsgm2bdMcHoiY115WK
    indexName: listing-ai
    indexHost: listing-ai-sap92q5.svc.aped-4627-b74a.pinecone.io
  activeProfile: QA
  server:
    port: 8000
  postgres:
    hostname: postgresql-postgresql
    username: sling_sales
    password: test
    port: 5432
    flags: ""
    dbName: ai
  redis:
    host: redis://redis-master:6379/2
  rabbitmq:
    host: rabbitmq
    virtualHost: /
    username: sling_sales
    password: test
  client:
    iam:
      basePath: http://sd-iam
    email:
      basePath: http://sd-emails