# Default values for sd-data-migrations.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1
namespace: sling-sales

image:
  repository: nexus.sling-dev.com:8023/sling/sd-data-migrations
  tag: latest
  pullPolicy: Always

imagePullSecrets:
  - name: registry-credentials
nameOverride: ""
fullnameOverride: ""

deployment:
  annotations:
    buildNumber: "replace-this"

service:
  type: ClusterIP
  port: 80

ingress:
  enabled: false
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths: []

  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

nodeSelector:
  service: sd-microservices-label

tolerations: []

affinity: {}

rollingUpdate:
  maxSurge: 1
  maxUnavailable: 0

appConfig:
  activeProfile: qa
  server:
    port: 8080
  rabbitmq:
    host: rabbitmq
    virtualHost: /
    username: sling_sales
    password: test
  postgres:
    hostname: postgresql-postgresql
    username: sling_sales
    password: test
    port: 5432
    flags: ""
  bulkPostgres:
    hostname: postgresql-postgresql
    username: sling_sales
    password: test
    port: 5432
    flags: ""
  reportPostgres:
    hostname: postgresql-postgresql
    username: sling_sales
    password: test
    port: 5432
    flags: ""
  elasticsearch:
    host: elasticsearch-master
  newElasticsearch:
    host: new-elasticsearch-master
    port: 9201
  billing:
    chargebee:
      siteName: kylas-sandbox-test
      apiKey: test_mWJz7H1EpFqhEadwHRPJU5O3E1ZQddMe
  s3:
    key: updated-by-jenkins
    secret: updated-by-jenkins
    companyLogo:
      endpoint: https://sgp1.digitaloceanspaces.com
      region: sgp1
      bucketName: company-logo
    lcf:
      key: updated-by-jenkins
      secret: updated-by-jenkins
      endpoint: https://sgp1.digitaloceanspaces.com
      region: sgp1
      bucketName: kylas-assets
      folderName: lead-capture-forms/qa
    utilizationReport:
      key: updated-by-jenkins
      secret: updated-by-jenkins
      folderName: qa/
      bucketName: usage-report
    import:
      endpoint: https://sgp1.digitaloceanspaces.com
      region: sgp1
      bucketName: import-service
  actuator:
    health:
      db:
        enabled: false
  gmail:
    oauth:
      clientId: 'some-id'
  outlook:
    oauth:
      clientId: 'some-id'

  email:
    credential:
      encryption:
        secret: 'some-secret'
        iv: 'some-iv'
  app:
    default:
      plan:
        name: ELEVATE_MONTHLY
  mixpanel:
    project:
      token: 'some-token'
  zoho:
    analytics:
      clientId: abc
      clientSecret: this-is-a-secret
      refreshToken: this-is-refresh-token
      orgId: '1122'
      workspaceId: '12312432'
      dailyUsage:
        viewId: '123222'
      tenantDailyUsage:
        view-id: 123223
      records:
        limit: 10