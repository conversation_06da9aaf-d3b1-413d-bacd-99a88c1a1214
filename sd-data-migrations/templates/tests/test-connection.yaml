apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "sd-data-migrations.fullname" . }}-test-connection"
  labels:
{{ include "sd-data-migrations.labels" . | indent 4 }}
  annotations:
    "helm.sh/hook": test-success
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args:  ['{{ include "sd-data-migrations.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
