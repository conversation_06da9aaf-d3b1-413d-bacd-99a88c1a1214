apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "sd-data-migrations.fullname" . }}
  labels:
{{ include "sd-data-migrations.labels" . | indent 4 }}
  namespace: {{ .Values.namespace }}
spec:
  replicas: {{ .Values.replicaCount }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: {{ .Values.rollingUpdate.maxSurge }}
      maxUnavailable: {{ .Values.rollingUpdate.maxUnavailable }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "sd-data-migrations.name" . }}
      app.kubernetes.io/instance: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app.kubernetes.io/name: {{ include "sd-data-migrations.name" . }}
        app.kubernetes.io/instance: {{ .Release.Name }}
        buildNumber: {{ .Values.deployment.annotations.buildNumber | quote }}
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "{{ .Values.appConfig.server.port }}"
        prometheus.io/path: "/actuator/prometheus"
        prometheus.io/scheme: "http"
    spec:
    {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
    {{- end }}
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: service-port
              containerPort: {{ .Values.appConfig.server.port }}
          livenessProbe:
            httpGet:
              path: /actuator/health
              port: service-port
            initialDelaySeconds: 60
            periodSeconds: 30
            timeoutSeconds: 10
          readinessProbe:
            httpGet:
              path: /actuator/health
              port: service-port
            initialDelaySeconds: 60
            periodSeconds: 30
            timeoutSeconds: 10
          volumeMounts:
            - mountPath: /usr/src/app/config/
              name: application-config
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      volumes:
        - name: application-config
          configMap:
            name: {{ .Chart.Name }}-config
            items:
              - key: application.yml
                path: application.yml
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
