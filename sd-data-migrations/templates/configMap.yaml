apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Chart.Name }}-config
  namespace: {{ .Values.namespace }}
data:
  application.yml: |-
    spring:
     profiles:
       active : {{ .Values.appConfig.activeProfile }}
     rabbitmq:
       host: {{ .Values.appConfig.rabbitmq.host }}
       username: {{ .Values.appConfig.rabbitmq.username }}
       password: {{ .Values.appConfig.rabbitmq.password }}
       virtual-host: {{ .Values.appConfig.rabbitmq.virtualHost }}
    datasource:
      deal:
       url: jdbc:postgresql://{{ .Values.appConfig.postgres.hostname }}:{{ .Values.appConfig.postgres.port }}/deal{{ .Values.appConfig.postgres.flags }}
       username: {{ .Values.appConfig.postgres.username }}
       password: {{ .Values.appConfig.postgres.password }}
      report:
       url: jdbc:postgresql://{{ .Values.appConfig.reportPostgres.hostname }}:{{ .Values.appConfig.reportPostgres.port }}/report{{ .Values.appConfig.reportPostgres.flags }}
       username: {{ .Values.appConfig.reportPostgres.username }}
       password: {{ .Values.appConfig.reportPostgres.password }}
      iam:
       url: jdbc:postgresql://{{ .Values.appConfig.postgres.hostname }}:{{ .Values.appConfig.postgres.port }}/iam{{ .Values.appConfig.postgres.flags }}
       username: {{ .Values.appConfig.postgres.username }}
       password: {{ .Values.appConfig.postgres.password }}
      config:
       url: jdbc:postgresql://{{ .Values.appConfig.postgres.hostname }}:{{ .Values.appConfig.postgres.port }}/config{{ .Values.appConfig.postgres.flags }}
       username: {{ .Values.appConfig.postgres.username }}
       password: {{ .Values.appConfig.postgres.password }}
      sales:
       url: jdbc:postgresql://{{ .Values.appConfig.postgres.hostname }}:{{ .Values.appConfig.postgres.port }}/sales{{ .Values.appConfig.postgres.flags }}
       username: {{ .Values.appConfig.postgres.username }}
       password: {{ .Values.appConfig.postgres.password }}
      company:
       url: jdbc:postgresql://{{ .Values.appConfig.postgres.hostname }}:{{ .Values.appConfig.postgres.port }}/company{{ .Values.appConfig.postgres.flags }}
       username: {{ .Values.appConfig.postgres.username }}
       password: {{ .Values.appConfig.postgres.password }}
      import:
       url: jdbc:postgresql://{{ .Values.appConfig.postgres.hostname }}:{{ .Values.appConfig.postgres.port }}/import{{ .Values.appConfig.postgres.flags }}
       username: {{ .Values.appConfig.postgres.username }}
       password: {{ .Values.appConfig.postgres.password }}
      search:
       url: jdbc:postgresql://{{ .Values.appConfig.postgres.hostname }}:{{ .Values.appConfig.postgres.port }}/search{{ .Values.appConfig.postgres.flags }}
       username: {{ .Values.appConfig.postgres.username }}
       password: {{ .Values.appConfig.postgres.password }}
      meeting:
       url: jdbc:postgresql://{{ .Values.appConfig.postgres.hostname }}:{{ .Values.appConfig.postgres.port }}/meetings{{ .Values.appConfig.postgres.flags }}
       username: {{ .Values.appConfig.postgres.username }}
       password: {{ .Values.appConfig.postgres.password }}
      email:
       url: jdbc:postgresql://{{ .Values.appConfig.postgres.hostname }}:{{ .Values.appConfig.postgres.port }}/emails{{ .Values.appConfig.postgres.flags }}
       username: {{ .Values.appConfig.postgres.username }}
       password: {{ .Values.appConfig.postgres.password }}
      productivity:
       url: jdbc:postgresql://{{ .Values.appConfig.postgres.hostname }}:{{ .Values.appConfig.postgres.port }}/productivity{{ .Values.appConfig.postgres.flags }}
       username: {{ .Values.appConfig.postgres.username }}
       password: {{ .Values.appConfig.postgres.password }}
      workflow:
       url: jdbc:postgresql://{{ .Values.appConfig.postgres.hostname }}:{{ .Values.appConfig.postgres.port }}/workflow{{ .Values.appConfig.postgres.flags }}
       username: {{ .Values.appConfig.postgres.username }}
       password: {{ .Values.appConfig.postgres.password }}
      product:
       url: jdbc:postgresql://{{ .Values.appConfig.postgres.hostname }}:{{ .Values.appConfig.postgres.port }}/product{{ .Values.appConfig.postgres.flags }}
       username: {{ .Values.appConfig.postgres.username }}
       password: {{ .Values.appConfig.postgres.password }}
      call:
       url: jdbc:postgresql://{{ .Values.appConfig.postgres.hostname }}:{{ .Values.appConfig.postgres.port }}/call{{ .Values.appConfig.postgres.flags }}
       username: {{ .Values.appConfig.postgres.username }}
       password: {{ .Values.appConfig.postgres.password }}
      dashboard:
       url: jdbc:postgresql://{{ .Values.appConfig.postgres.hostname }}:{{ .Values.appConfig.postgres.port }}/dashboard{{ .Values.appConfig.postgres.flags }}
       username: {{ .Values.appConfig.postgres.username }}
       password: {{ .Values.appConfig.postgres.password }}
      message:
       url: jdbc:postgresql://{{ .Values.appConfig.postgres.hostname }}:{{ .Values.appConfig.postgres.port }}/message{{ .Values.appConfig.postgres.flags }}
       username: {{ .Values.appConfig.postgres.username }}
       password: {{ .Values.appConfig.postgres.password }}
      marketplace:
       url: jdbc:postgresql://{{ .Values.appConfig.postgres.hostname }}:{{ .Values.appConfig.postgres.port }}/marketplace{{ .Values.appConfig.postgres.flags }}
       username: {{ .Values.appConfig.postgres.username }}
       password: {{ .Values.appConfig.postgres.password }}
      notification:
       url: jdbc:postgresql://{{ .Values.appConfig.postgres.hostname }}:{{ .Values.appConfig.postgres.port }}/notification{{ .Values.appConfig.postgres.flags }}
       username: {{ .Values.appConfig.postgres.username }}
       password: {{ .Values.appConfig.postgres.password }}
      document:
       url: jdbc:postgresql://{{ .Values.appConfig.bulkPostgres.hostname }}:{{ .Values.appConfig.bulkPostgres.port }}/document{{ .Values.appConfig.bulkPostgres.flags }}
       username: {{ .Values.appConfig.bulkPostgres.username }}
       password: {{ .Values.appConfig.bulkPostgres.password }}
      orderManagement:
       url: jdbc:postgresql://{{ .Values.appConfig.bulkPostgres.hostname }}:{{ .Values.appConfig.bulkPostgres.port }}/order_management{{ .Values.appConfig.bulkPostgres.flags }}
       username: {{ .Values.appConfig.bulkPostgres.username }}
       password: {{ .Values.appConfig.bulkPostgres.password }}
      forex:
       url: jdbc:postgresql://{{ .Values.appConfig.bulkPostgres.hostname }}:{{ .Values.appConfig.bulkPostgres.port }}/forex{{ .Values.appConfig.bulkPostgres.flags }}
       username: {{ .Values.appConfig.bulkPostgres.username }}
       password: {{ .Values.appConfig.bulkPostgres.password }}
      feed:
       url: jdbc:postgresql://{{ .Values.appConfig.bulkPostgres.hostname }}:{{ .Values.appConfig.bulkPostgres.port }}/feed{{ .Values.appConfig.bulkPostgres.flags }}
       username: {{ .Values.appConfig.bulkPostgres.username }}
       password: {{ .Values.appConfig.bulkPostgres.password }}
      scoreRule:
       url: jdbc:postgresql://{{ .Values.appConfig.bulkPostgres.hostname }}:{{ .Values.appConfig.bulkPostgres.port }}/score_rule{{ .Values.appConfig.bulkPostgres.flags }}
       username: {{ .Values.appConfig.bulkPostgres.username }}
       password: {{ .Values.appConfig.bulkPostgres.password }}
    elasticsearch:
      host: {{ .Values.appConfig.elasticsearch.host }}
    newElasticsearch:
      host: {{ .Values.appConfig.newElasticsearch.host }}
      port: {{ .Values.appConfig.newElasticsearch.port }}
    billing:
      chargebee:
        site-name: {{ .Values.appConfig.billing.chargebee.siteName }}
        api-key: {{ .Values.appConfig.billing.chargebee.apiKey }}
    s3:
      key: {{ .Values.appConfig.s3.key }}
      secret: {{ .Values.appConfig.s3.secret }}
      company-logo:
        endpoint: {{ .Values.appConfig.s3.companyLogo.endpoint }}
        region: {{ .Values.appConfig.s3.companyLogo.region }}
        bucket-name: {{ .Values.appConfig.s3.companyLogo.bucketName }}
      lcf:
        key: {{ .Values.appConfig.s3.lcf.key }}
        secret: {{ .Values.appConfig.s3.lcf.secret }}
        endpoint: {{ .Values.appConfig.s3.lcf.endpoint }}
        region: {{ .Values.appConfig.s3.lcf.region }}
        bucket-name: {{ .Values.appConfig.s3.lcf.bucketName }}
        folder-name: {{ .Values.appConfig.s3.lcf.folderName }}
      utilization-report:
        key: {{ .Values.appConfig.s3.utilizationReport.key }}
        secret: {{ .Values.appConfig.s3.utilizationReport.secret }}
        folder-name: {{ .Values.appConfig.s3.utilizationReport.folderName }}
        bucket-name: {{ .Values.appConfig.s3.utilizationReport.bucketName }}
      import:
        endpoint: {{ .Values.appConfig.s3.import.endpoint }}
        region: {{ .Values.appConfig.s3.import.region }}
        bucket-name: {{ .Values.appConfig.s3.import.bucketName }}
    server:
     port: {{ .Values.appConfig.server.port }}
    gmail:
      oauth:
        clientId: {{ .Values.appConfig.gmail.oauth.clientId }}
    outlook:
      oauth:
        clientId: {{ .Values.appConfig.outlook.oauth.clientId }}
    email:
      credential:
        encryption:
          secret: {{ .Values.appConfig.email.credential.encryption.secret }}
          iv: {{ .Values.appConfig.email.credential.encryption.iv }}
    app:
      default:
        plan:
          name: {{ .Values.appConfig.app.default.plan.name }}
    mixpanel:
      project:
        token: {{ .Values.appConfig.mixpanel.project.token }}
    zoho:
      analytics:
        client-id: {{ .Values.appConfig.zoho.analytics.clientId }}
        client-secret: {{ .Values.appConfig.zoho.analytics.clientSecret }}
        refresh-token: {{ .Values.appConfig.zoho.analytics.refreshToken }}
        org-id: {{ .Values.appConfig.zoho.analytics.orgId }}
        workspace-id: {{ .Values.appConfig.zoho.analytics.workspaceId }}
        dailyUsage:
          view-id: {{ .Values.appConfig.zoho.analytics.dailyUsage.viewId }}
        tenantDailyUsage:
          view-id: {{ .Values.appConfig.zoho.analytics.tenantDailyUsage.viewId }}
        records: 
          limit: {{ .Values.appConfig.zoho.analytics.records.limit }}