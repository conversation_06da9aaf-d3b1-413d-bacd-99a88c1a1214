apiVersion: batch/v1
kind: Job
metadata:
  name: {{ include "sd-deal.fullname" . }}
  labels:
{{ include "sd-deal.labels" . | indent 4 }}
  namespace: {{ .Values.namespace }}
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade
    "helm.sh/hook-weight": "1"
    "helm.sh/hook-delete-policy": before-hook-creation
spec:
  template:
    spec:
    {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
    {{- end }}
      containers:
        - name: "{{ .Chart.Name }}-db-migration"
          image: "{{ .Values.image.migrationRepository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
            - name: FLYWAY_USER
              value: {{ .Values.appConfig.postgres.username }}
            - name: FLYWAY_PASSWORD
              value: {{ .Values.appConfig.postgres.password }}
            - name: FLYWAY_URL
              value: jdbc:postgresql://{{ .Values.appConfig.postgres.hostname }}:{{ .Values.appConfig.postgres.port }}/deal{{ .Values.appConfig.postgres.flags }}
          args:
            - repair
            - migrate
      restartPolicy: Never
