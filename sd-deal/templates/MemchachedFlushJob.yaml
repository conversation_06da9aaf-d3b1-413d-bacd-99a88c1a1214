apiVersion: batch/v1
kind: Job
metadata:
  name: {{ include "sd-deal.fullname" . }}
  labels:
{{ include "sd-deal.labels" . | indent 4 }}
  namespace: {{ .Values.namespace }}
  annotations:
    "helm.sh/hook": post-install,post-upgrade
    "helm.sh/hook-weight": "1"
    "helm.sh/hook-delete-policy": before-hook-creation
spec:
  template:
    spec:
    {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
    {{- end }}
      containers:
        - name: "{{ .Chart.Name }}-memcached-flush"
          image: "{{ .Values.image.memcachedFlushRepository }}:latest"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          args:
            - memcached
      restartPolicy: Never