apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Chart.Name }}-config
  namespace: {{ .Values.namespace }}
data:
  application.yml: |-
    spring:
      profiles:
        active : {{ .Values.appConfig.activeProfile }}
      datasource:
        url: jdbc:postgresql://{{ .Values.appConfig.postgres.hostname }}:{{ .Values.appConfig.postgres.port }}/deal{{ .Values.appConfig.postgres.flags }}
        username: {{ .Values.appConfig.postgres.username }}
        password: {{ .Values.appConfig.postgres.password }}
        hikari:
          maximum-pool-size: {{ .Values.appConfig.postgres.hikari.maximumPoolSize }}
      rabbitmq:
        host: {{ .Values.appConfig.rabbitmq.host }}
        port: 5672
        username: {{ .Values.appConfig.rabbitmq.username }}
        password: {{ .Values.appConfig.rabbitmq.password }}
        virtual-host: {{ .Values.appConfig.rabbitmq.virtualHost }}
    server:
      port: {{ .Values.appConfig.server.port }}
    client:
      iam:
        basePath: {{ .Values.appConfig.client.iam.basePath }}
      sales:
        basePath: {{ .Values.appConfig.client.sales.basePath }}
      product:
        basePath: {{ .Values.appConfig.client.product.basePath }}
      company:
        basePath: {{ .Values.appConfig.client.company.basePath }}
      config:
        basePath: {{ .Values.appConfig.client.config.basePath }}
      search:
        basePath: {{ .Values.appConfig.client.search.basePath }}
      forex:
        basePath: {{ .Values.appConfig.client.forex.basePath }}
    memcached:
      host: {{ .Values.appConfig.memcached.host }}
