# Default values for sd-config.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 2
namespace: sling-sales

image:
  repository: nexus.sling-dev.com:8023/sling/sd-config
  memcachedFlushRepository: nexus.sling-dev.com:8023/memcached/memcached-flush
  migrationRepository: nexus.sling-dev.com:8023/sling/sd-config/db-migration
  tag: latest
  pullPolicy: Always

imagePullSecrets:
  - name: registry-credentials
nameOverride: ""
fullnameOverride: ""

service:
  type: ClusterIP
  port: 80

deployment:
  annotations:
    buildNumber: "replace-this"

ingress:
  enabled: false
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths: []

  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
   requests:
  #   cpu: 100m
     memory: 1800Mi

autoscaling:
  enabled: false
  minReplicas: 2
  maxReplicas: 6
  #    targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 90

nodeSelector: {}

tolerations: []

affinity:
  nodeAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 75
        preference:
          matchExpressions:
            - key: highMemory
              operator: In
              values:
                - sd-high-memory-service-pods
      - weight: 50
        preference:
          matchExpressions:
            - key: service
              operator: In
              values:
                - sd-microservices-label

rollingUpdate:
  maxSurge: 1
  maxUnavailable: 0

appConfig:
  server:
    port: 8080
  rabbitmq:
    host: rabbitmq
    virtualHost: /
    username: sling_sales
    password: test
  postgres:
    hostname: postgresql-postgresql
    port: 5432
    flags: ""
    username: sling_sales
    password: test
  elasticsearch:
    host: elasticsearch-master
    port: 9200
  newElasticsearch:
    host: new-elasticsearch-master
    port: 9201
  client:
    iam:
      basePath: http://sd-iam
    sales:
      basePath: http://sd-sales
    company:
      basePath: http://sd-company
    deal:
      basePath: http://sd-deal
    product:
      basePath: http://sd-product
    productivity:
      basePath: http://sd-productivity
  spring:
    datasource:
      hikari:
        maximumPoolSize: 15
        minimumIdle: 5
        idleTimeout: 30000
        poolName: SpringBootJPAHikariCP
        connectionTimeout: 30000
  actuator:
    health:
      db:
        enabled: false
  memcached:
    host: memcached