apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Chart.Name }}-config
  namespace: {{ .Values.namespace }}
data:
  application.properties: |
    server.port: {{ .Values.appConfig.server.port }}
    core.rabbitmq.host: {{ .Values.appConfig.rabbitmq.host }}
    core.rabbitmq.username: {{ .Values.appConfig.rabbitmq.username }}
    core.rabbitmq.password: {{ .Values.appConfig.rabbitmq.password }}
    core.rabbitmq.virtualHost: {{ .Values.appConfig.rabbitmq.virtualHost }}
    spring.datasource.url: jdbc:postgresql://{{ .Values.appConfig.postgres.hostname }}:{{ .Values.appConfig.postgres.port }}/config{{ .Values.appConfig.postgres.flags }}
    spring.datasource.username: {{ .Values.appConfig.postgres.username }}
    spring.datasource.password: {{ .Values.appConfig.postgres.password }}
    client.iam.basePath: {{ .Values.appConfig.client.iam.basePath }}
    client.sales.basePath: {{ .Values.appConfig.client.sales.basePath }}
    client.company.basePath: {{ .Values.appConfig.client.company.basePath }}
    client.deal.basePath: {{ .Values.appConfig.client.deal.basePath }}
    client.product.basePath: {{ .Values.appConfig.client.product.basePath }}
    client.productivity.basePath: {{ .Values.appConfig.client.productivity.basePath }}
    elasticsearch.host: {{ .Values.appConfig.elasticsearch.host }}
    elasticsearch.port: {{ .Values.appConfig.elasticsearch.port }}
    newElasticsearch.host: {{ .Values.appConfig.newElasticsearch.host }}
    newElasticsearch.port: {{ .Values.appConfig.newElasticsearch.port }}
    logging.level.root: WARN
    logging.level.org.springframework.web: WARN
    logging.level.org.hibernate: ERROR
    spring.datasource.hikari.maximum-pool-size: {{ .Values.appConfig.spring.datasource.hikari.maximumPoolSize }}
    spring.datasource.hikari.minimum-idle: {{ .Values.appConfig.spring.datasource.hikari.minimumIdle }}
    spring.datasource.hikari.idle-timeout: {{ .Values.appConfig.spring.datasource.hikari.idleTimeout }}
    spring.datasource.hikari.pool-name: {{ .Values.appConfig.spring.datasource.hikari.poolName }}
    spring.datasource.hikari.connection-timeout: {{ .Values.appConfig.spring.datasource.hikari.connectionTimeout }}
    management.health.db.enabled: {{ .Values.appConfig.actuator.health.db.enabled }}
    memcached.host: {{ .Values.appConfig.memcached.host }}