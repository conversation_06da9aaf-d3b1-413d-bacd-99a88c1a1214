apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "sd-config.fullname" . }}
  labels:
{{ include "sd-config.labels" . | indent 4 }}
  namespace: {{ .Values.namespace }}
spec:
  replicas: {{ .Values.replicaCount }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: {{ .Values.rollingUpdate.maxSurge }}
      maxUnavailable: {{ .Values.rollingUpdate.maxUnavailable }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "sd-config.name" . }}
      app.kubernetes.io/instance: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app.kubernetes.io/name: {{ include "sd-config.name" . }}
        app.kubernetes.io/instance: {{ .Release.Name }}
        buildNumber: {{ .Values.deployment.annotations.buildNumber | quote }}
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "{{ .Values.appConfig.server.port }}"
        prometheus.io/path: "/prometheus"
        prometheus.io/scheme: "http"
    spec:
    {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
    {{- end }}
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: service-port
              containerPort: {{ .Values.appConfig.server.port }}
          livenessProbe:
            httpGet:
              path: /health
              port: service-port
            initialDelaySeconds: 60
            periodSeconds: 30
            timeoutSeconds: 5
          readinessProbe:
            httpGet:
              path: /health
              port: service-port
            initialDelaySeconds: 60
            periodSeconds: 30
            timeoutSeconds: 5
          volumeMounts:
            - mountPath: /usr/src/app/config/
              name: application-config
          {{- if .Values.autoscaling.enabled }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          {{- end }}
      volumes:
        - name: application-config
          configMap:
            name: {{ .Chart.Name }}-config
            items:
              - key: application.properties
                path: application.properties
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
