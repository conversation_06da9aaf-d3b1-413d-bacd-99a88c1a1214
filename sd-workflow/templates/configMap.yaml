apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Chart.Name }}-config
  namespace: {{ .Values.namespace }}
data:
  application.yml: |-
    spring:
      profiles:
        active : {{ .Values.appConfig.activeProfile }}
      datasource:
        url: jdbc:postgresql://{{ .Values.appConfig.postgres.hostname }}:{{ .Values.appConfig.postgres.port }}/workflow{{ .Values.appConfig.postgres.flags }}
        username: {{ .Values.appConfig.postgres.username }}
        password: {{ .Values.appConfig.postgres.password }}
      rabbitmq:
        host: {{ .Values.appConfig.rabbitmq.host }}
        username: {{ .Values.appConfig.rabbitmq.username }}
        password: {{ .Values.appConfig.rabbitmq.password }}
        virtual-host: {{ .Values.appConfig.rabbitmq.virtualHost }}
    server:
      port: {{ .Values.appConfig.server.port }}
    security:
      crypto:
        key: {{ .Values.appConfig.security.crypto.key }}
    client:
      iam:
        basePath: http://sd-iam
      deal:
        basePath: http://sd-deal
      config:
        basePath: http://sd-config
      search:
        basePath: http://sd-search
      product:
        basePath: http://sd-product
      company:
        basePath: http://sd-company
      sales:
        basePath: http://sd-sales
      call:
        basePath: http://sd-call
      productivity:
        basePath: http://sd-productivity
      meeting:
        basePath: http://sd-meetings
      email:
        basePath: http://sd-emails
      marketplace:
        basePath: http://sd-marketplace