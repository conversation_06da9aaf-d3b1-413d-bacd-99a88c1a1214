# Default values for sd-email-engine.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: postalsys/emailengine
  tag: v2.42.0
  pullPolicy: Always

imagePullSecrets: {}

nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name:

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 80

ingress:
  enabled: false
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths: []
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as <PERSON>ku<PERSON>. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

nodeSelector: {}

tolerations: []

affinity:
  nodeAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 75
        preference:
          matchExpressions:
            - key: highMemory
              operator: In
              values:
                - sd-high-memory-service-pods
      - weight: 50
        preference:
          matchExpressions:
            - key: service
              operator: In
              values:
                - sd-microservices-label

redis:
  host: "redis://redis-master:6379/2"
engine:
  commandTimeout: "20s"
  submitWorkerCount: "4"
  imapWorkerCount: "10"
  webhookWorkerCount: "3"
  maxAttachmentSize: "25M"
  submitQueueConcurrencyCount: "10"
  notifyQueueConcurrencyCount: "5"
  setupDelay: "1s"
