apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "sd-email-engine.fullname" . }}
  labels:
    {{- include "sd-email-engine.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "sd-email-engine.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      labels:
        {{- include "sd-email-engine.selectorLabels" . | nindent 8 }}
    spec:
    {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
    {{- end }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
            - name: EENGINE_REDIS
              value: {{ .Values.redis.host }}
            - name: EENGINE_TIMEOUT
              value: {{ .Values.engine.commandTimeout }}
            - name: EENGINE_WORKERS_SUBMIT
              value: "{{ .Values.engine.submitWorkerCount }}"
            - name: EENGINE_WORKERS
              value: "{{ .Values.engine.imapWorkerCount }}"
            - name: EENGINE_WORKERS_WEBHOOKS
              value: "{{ .Values.engine.webhookWorkerCount }}"
            - name: EENGINE_MAX_SIZE
              value: "{{ .Values.engine.maxAttachmentSize }}"
            - name: EENGINE_SUBMIT_QC
              value: "{{ .Values.engine.submitQueueConcurrencyCount }}"
            - name: EENGINE_NOTIFY_QC
              value: "{{ .Values.engine.notifyQueueConcurrencyCount }}"
            - name: EENGINE_CONNECION_SETUP_DELAY
              value: "{{ .Values.engine.setupDelay }}"
          ports:
            - name: http
              containerPort: 3000
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /
              port: http
            initialDelaySeconds: 60
            periodSeconds: 25
            timeoutSeconds: 5
          readinessProbe:
            httpGet:
              path: /
              port: http
            initialDelaySeconds: 60
            periodSeconds: 25
            timeoutSeconds: 5
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
