apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "sd-email-engine.fullname" . }}-test-connection"
  labels:
{{ include "sd-email-engine.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test-success
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args:  ['{{ include "sd-email-engine.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
