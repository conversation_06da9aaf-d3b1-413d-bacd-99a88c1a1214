apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Chart.Name }}-config
  namespace: {{ .Values.namespace }}
data:
  application.yml: |-
    spring:
      profiles:
        active : {{ .Values.appConfig.activeProfile }}
      datasource:
        url: jdbc:postgresql://{{ .Values.appConfig.postgres.hostname }}:{{ .Values.appConfig.postgres.port }}/notification{{ .Values.appConfig.postgres.flags }}
        username: {{ .Values.appConfig.postgres.username }}
        password: {{ .Values.appConfig.postgres.password }}
      rabbitmq:
        host: {{ .Values.appConfig.rabbitmq.host }}
        username: {{ .Values.appConfig.rabbitmq.username }}
        password: {{ .Values.appConfig.rabbitmq.password }}
        virtual-host: {{ .Values.appConfig.rabbitmq.virtualHost }}
    server:
      port: {{ .Values.appConfig.server.port }}
    client:
      iam:
        basePath: http://sd-iam
      sales:
        basePath: http://sd-sales
      search:
        basePath: http://sd-search
      config:
        basePath: http://sd-config
      deal:
        basePath: http://sd-deal
    firebase:
      config:
        type: service_account
        auth_uri: https://accounts.google.com/o/oauth2/auth
        token_uri: https://oauth2.googleapis.com/token
        auth_provider_x509_cert_url: https://www.googleapis.com/oauth2/v1/certs
        project_id: {{ .Values.appConfig.firebase.config.project_id }}
        private_key_id: {{ .Values.appConfig.firebase.config.private_key_id }}
        private_key: {{ .Values.appConfig.firebase.config.private_key }}
        client_email: {{ .Values.appConfig.firebase.config.client_email }}
        client_id: {{ .Values.appConfig.firebase.config.client_id }}
        client_x509_cert_url: {{ .Values.appConfig.firebase.config.client_x509_cert_url }}
    baseUrl: {{ .Values.appConfig.baseUrl }}
    app:
      notification:
        title: {{ .Values.appConfig.app.notification.title }}