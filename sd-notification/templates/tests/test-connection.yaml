apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "sd-notification.fullname" . }}-test-connection"
  labels:
{{ include "sd-notification.labels" . | indent 4 }}
  annotations:
    "helm.sh/hook": test-success
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args:  ['{{ include "sd-notification.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
