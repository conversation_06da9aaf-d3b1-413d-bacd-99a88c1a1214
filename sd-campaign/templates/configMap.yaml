apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Chart.Name }}-config
  namespace: {{ .Values.namespace }}
data:
  application.yml: |-
    spring:
      profiles:
        active : {{ .Values.appConfig.activeProfile }}
      datasource:
        url: jdbc:postgresql://{{ .Values.appConfig.postgres.hostname }}:{{ .Values.appConfig.postgres.port }}/campaign{{ .Values.appConfig.postgres.flags }}
        username: {{ .Values.appConfig.postgres.username }}
        password: {{ .Values.appConfig.postgres.password }}
        hikari:
          maximum-pool-size: {{ .Values.appConfig.postgres.hikari.maximumPoolSize }}
      rabbitmq:
        host: {{ .Values.appConfig.rabbitmq.host }}
        username: {{ .Values.appConfig.rabbitmq.username }}
        password: {{ .Values.appConfig.rabbitmq.password }}
        virtual-host: {{ .Values.appConfig.rabbitmq.virtualHost }}
    server:
      port: {{ .Values.appConfig.server.port }}
    client:
      iam:
        basePath: {{ .Values.appConfig.client.iam.basePath }}
      forex:
        basePath: {{ .Values.appConfig.client.forex.basePath }}
      search:
        basePath: {{ .Values.appConfig.client.search.basePath }}
      config:
        basePath: {{ .Values.appConfig.client.config.basePath }}