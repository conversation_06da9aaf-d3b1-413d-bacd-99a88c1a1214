apiVersion: v1
kind: Service
metadata:
  name: {{ include "sd-ui.fullname" . }}
  labels:
{{ include "sd-ui.labels" . | indent 4 }}
    buildNumber: {{ .Values.deployment.annotations.buildNumber | quote }}
  namespace: {{ .Values.namespace }}
  {{ if ne .Values.service.certificateId "N/A" }}
  annotations:
    service.beta.kubernetes.io/do-loadbalancer-protocol: "http"
    service.beta.kubernetes.io/do-loadbalancer-algorithm: "round_robin"
    service.beta.kubernetes.io/do-loadbalancer-tls-ports: "443"
    service.beta.kubernetes.io/do-loadbalancer-certificate-id: {{ .Values.service.certificateId }}
    service.beta.kubernetes.io/do-loadbalancer-redirect-http-to-https: "true"
  {{ end }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - name: http
      protocol: TCP
      port: {{ .Values.service.port }}
      targetPort: 80
    {{ if ne .Values.service.certificateId "N/A" }}
    - name: https
      protocol: TCP
      port: {{ .Values.service.sslPort }}
      targetPort: 80
    {{ end }}
  selector:
    app.kubernetes.io/name: {{ include "sd-ui.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
