apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "sd-webhook.fullname" . }}
  labels:
{{ include "sd-webhook.labels" . | indent 4 }}
  namespace: {{ .Values.namespace }}
spec:
  replicas: {{ .Values.replicaCount }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: {{ .Values.rollingUpdate.maxSurge }}
      maxUnavailable: {{ .Values.rollingUpdate.maxUnavailable }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "sd-webhook.name" . }}
      app.kubernetes.io/instance: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app.kubernetes.io/name: {{ include "sd-webhook.name" . }}
        app.kubernetes.io/instance: {{ .Release.Name }}
        buildNumber: {{ .Values.deployment.annotations.buildNumber | quote }}
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "{{ .Values.appConfig.server.port }}"
        prometheus.io/path: "/prometheus"
        prometheus.io/scheme: "http"
    spec:
    {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
    {{- end }}
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: service-port
              containerPort: {{ .Values.appConfig.server.port }}
          livenessProbe:
            httpGet:
              path: /health
              port: service-port
            initialDelaySeconds: 60
            periodSeconds: 30
            timeoutSeconds: 10
          readinessProbe:
            httpGet:
              path: /health
              port: service-port
            initialDelaySeconds: 60
            periodSeconds: 30
            timeoutSeconds: 10
          env:
          - name: DATABASE_HOST
            value: {{ .Values.appConfig.postgres.hostname }}
          - name: DATABASE_PORT
            value: {{ .Values.appConfig.postgres.port  | quote }}
          - name: DATABASE_USER
            value: {{ .Values.appConfig.postgres.username }}
          - name: DATABASE_PASSWORD
            value: {{ .Values.appConfig.postgres.password }}
          - name: DATABASE_NAME
            value: {{ .Values.appConfig.postgres.dbName }}
          - name: RAILS_MASTER_KEY
            value: {{ .Values.appConfig.credentials.masterKey }}
          - name: DATABASE_POOL
            value: {{ .Values.appConfig.postgres.poolSize | quote }}
          - name: RABBITMQ_USER
            value: {{ .Values.appConfig.rabbitmq.username }}
          - name: RABBITMQ_PASSWORD
            value: {{ .Values.appConfig.rabbitmq.password }}
          - name: RABBITMQ_VIRTUAL_HOST
            value: {{ .Values.appConfig.rabbitmq.virtualHost }}
          - name: RABBITMQ_HOST
            value: {{ .Values.appConfig.rabbitmq.host }}
          - name: RAILS_LOG_TO_STDOUT
            value: {{ .Values.appConfig.logoutput | quote }}
          - name: REDIS_HOST
            value: {{ .Values.appConfig.redis.host }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
